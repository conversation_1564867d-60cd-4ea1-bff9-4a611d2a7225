# Required libraries:
# pip install python-docx pandas openpyxl lxml tkinter

import docx
import pandas as pd
import os
import tkinter as tk
from tkinter import filedialog

# lxml is required by python-docx and used for xpath
import traceback  # For printing tracebacks

# Define the main WordprocessingML namespace URI
WPML_NAMESPACE = "http://schemas.openxmlformats.org/wordprocessingml/2006/main"


def try_convert_numeric(value):
    """
    Attempts to convert a string value to numeric (int or float).
    Returns the original value if conversion fails.
    Handles potential leading/trailing whitespace and common numeric formats.
    """
    if isinstance(value, (int, float)):
        return value  # Already numeric
    if not isinstance(value, str):
        return value  # Not a string, cannot convert

    cleaned_value = value.strip()
    if not cleaned_value:
        # Return None or np.nan for empty strings if preferred for numeric columns
        # return None
        return value  # Keep empty strings as they are

    # Try converting to integer first
    try:
        # Avoid converting things like "1.0" to int directly
        # Check if it looks like an integer before converting
        temp_float = float(cleaned_value)
        if temp_float == int(temp_float):
            return int(temp_float)
        # If not a perfect integer, fall through to float conversion
    except ValueError:
        pass  # Not a number that can be int

    # Try converting to float
    try:
        return float(cleaned_value)
    except ValueError:
        pass  # Not a float either

    # If neither conversion worked, return the original cleaned string
    return cleaned_value


def extract_tables_to_excel(word_filepath, excel_filepath):
    """
    Extracts all tables from a Word document (.docx) and saves each table
    to a separate sheet in an Excel file (.xlsx).
    Uses a two-phase approach: Read all data first, then write all data.
    Handles text within Content Controls (Structured Document Tags - SDT).
    Attempts to convert extracted text to numeric types before writing.
    Assumes the first row of each Word table is the header.
    *** Pads rows with empty strings to handle inconsistent column counts within a table. ***

    Args:
        word_filepath (str): The path to the input Word document.
        excel_filepath (str): The path where the output Excel file will be saved.
    """
    all_tables_data = []  # To store data from all tables in memory first

    # --- Phase 1: Read Data from Word Document ---
    print("--- Starting Phase 1: Reading Word Document ---")
    try:
        # Check if the Word file exists
        if not os.path.exists(word_filepath):
            print(f"Error: Word file not found at {word_filepath}")
            return

        # Load the Word document
        document = docx.Document(word_filepath)
        print(f"Opened Word document: {os.path.basename(word_filepath)}")
        print(f"Found {len(document.tables)} tables.")

        if not document.tables:
            print("No tables found in the document.")
            return

        # Iterate through each table in the document to extract data
        for i, table in enumerate(document.tables):
            print(f"  Reading Table {i + 1}...")
            table_data_raw = []  # Store raw rows before padding
            max_cols = 0  # Track max columns for this specific table

            for r_idx, row in enumerate(table.rows):
                row_data = []
                # Iterate through each cell in the row
                for c_idx, cell in enumerate(row.cells):
                    cell_content_parts = []
                    cell_element = (
                        cell._element
                    )  # Get the underlying lxml element for the cell (<w:tc>)

                    # Use XPath to find SDT elements within the cell
                    sdt_xpath = f".//*[local-name()='sdt' and namespace-uri()='{WPML_NAMESPACE}']"
                    sdt_elements = cell_element.xpath(sdt_xpath)

                    if sdt_elements:  # If SDTs are found
                        for sdt_element in sdt_elements:
                            text_xpath = ".//*[local-name()='sdtContent']//*[local-name()='p']//*[local-name()='r']//*[local-name()='t']"
                            text_elements = sdt_element.xpath(text_xpath)
                            for text_element in text_elements:
                                if text_element.text:
                                    cell_content_parts.append(text_element.text)
                    else:  # If no SDTs found, use standard paragraph/run iteration
                        for paragraph in cell.paragraphs:
                            for run in paragraph.runs:
                                cell_content_parts.append(run.text)

                    # Join the collected parts and clean up
                    cell_text = " ".join(cell_content_parts).replace("\n", " ").strip()

                    # Attempt numeric conversion for the cell text
                    converted_value = try_convert_numeric(cell_text)
                    row_data.append(converted_value)

                # Update max_cols if this row has more columns than seen so far IN THIS TABLE
                if len(row_data) > max_cols:
                    max_cols = len(row_data)

                # Append the row data to the raw list only if it contains non-empty content
                original_row_text = [str(item) for item in row_data]
                if any(
                    str(cell_content).strip() for cell_content in row_data
                ):  # Check if any cell has content
                    table_data_raw.append(row_data)
                # else:
                #     print(f"  Skipping completely empty row {r_idx+1} in Table {i+1}")

            # --- Padding Step ---
            if table_data_raw:  # Only pad if we actually collected rows
                print(
                    f"  Table {i + 1}: Max columns found: {max_cols}. Padding rows if necessary."
                )
                padded_table_data = []
                for row_list in table_data_raw:
                    # Calculate how many empty strings to add
                    padding_needed = max_cols - len(row_list)
                    if padding_needed > 0:
                        # Extend the row with empty strings
                        padded_row = row_list + [""] * padding_needed
                        padded_table_data.append(padded_row)
                    else:
                        # Row already has max length or potentially more (though unlikely)
                        # If it could be more, truncate: padded_table_data.append(row_list[:max_cols])
                        padded_table_data.append(row_list)

                # Add the fully processed (and padded) table data to the main list
                all_tables_data.append(padded_table_data)
                print(
                    f"  Successfully read and padded {len(padded_table_data)} rows for Table {i + 1}."
                )
            else:
                print(
                    f"  Table {i + 1} resulted in no data after processing rows, skipping."
                )

        print("--- Finished Phase 1: Reading Word Document ---")

    except ImportError as import_err:
        if "lxml" in str(import_err):
            print("Error: Required library 'lxml' not found or import failed.")
            print("Please ensure it is installed: pip install lxml")
            return
        else:
            print(f"An unexpected import error occurred: {import_err}")
            return
    except Exception as read_error:
        print("\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print(
            f"An error occurred during Phase 1 (Reading Word Document) for table {i + 1 if 'i' in locals() else 'unknown'}."
        )
        print("Traceback:")
        traceback.print_exc()
        print(f"Error details: {read_error}")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n")
        return  # Stop if reading fails

    # --- Phase 2: Write Data to Excel File ---
    print("\n--- Starting Phase 2: Writing to Excel File ---")
    if not all_tables_data:
        print(
            "No table data was collected from the Word document. Nothing to write to Excel."
        )
        return

    try:
        # Create a Pandas Excel writer ONLY AFTER reading is done
        print("Initializing ExcelWriter...")
        # Use xlsxwriter for potentially better handling of large files or complex formatting if needed
        # Or stick with openpyxl if it works fine
        with pd.ExcelWriter(excel_filepath, engine="openpyxl") as writer:
            print("ExcelWriter initialized successfully.")
            written_sheets_count = 0
            # Iterate through the collected (and padded) table data
            for i, table_data in enumerate(all_tables_data):
                # Define the sheet name using the original table index + 1
                sheet_name = f"Table_{i + 1}"
                print(
                    f"  Processing collected data for sheet '{sheet_name}' (Original Table {i + 1})..."
                )

                df = None  # Initialize df to None

                # Now, table_data contains rows that are all padded to the same length (max_cols for that table)
                if table_data:  # Check if table_data (post-padding) is not empty
                    if (
                        len(table_data) > 0
                    ):  # Should always be true if table_data is not empty
                        headers = table_data[
                            0
                        ]  # Get the first row as headers (now padded)
                        data = table_data[
                            1:
                        ]  # Get the rest of the rows as data (also padded)

                        # Ensure headers are strings and handle potential duplicates
                        headers = [
                            str(h) for h in headers
                        ]  # Ensure all headers are strings
                        unique_headers = []
                        counts = {}
                        for h in headers:
                            if h in counts:
                                counts[h] += 1
                                unique_headers.append(f"{h}_{counts[h]}")
                            else:
                                counts[h] = 0  # Start count at 0 for first occurrence
                                unique_headers.append(h)
                        headers = unique_headers

                        # Create DataFrame - lengths should now match
                        df = pd.DataFrame(data, columns=headers)
                        print(
                            f"  DataFrame created with shape {df.shape} using first row as header."
                        )

                    # Removed the 'elif len(table_data) == 1' case as it's covered above.
                    # An empty DataFrame with only headers is created if len(data) == 0.

                else:
                    # This case should not be reached if table_data was added to all_tables_data
                    print(
                        f"  Table data for sheet '{sheet_name}' is unexpectedly empty after padding. Skipping."
                    )
                    continue  # Skip to the next table

                # Check if DataFrame was successfully created
                if df is not None:
                    # Write the DataFrame to a specific sheet
                    try:
                        print(
                            f"  Attempting to write sheet '{sheet_name}' (shape {df.shape})..."
                        )
                        # Write the DataFrame, including the header row from the DataFrame
                        df.to_excel(
                            writer, sheet_name=sheet_name, index=False, header=True
                        )
                        print(f"  Successfully wrote sheet '{sheet_name}'.")
                        written_sheets_count += 1
                    except Exception as excel_write_error:
                        print(
                            "\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
                        )
                        print(
                            f"ERROR: Failed specifically during df.to_excel() for sheet '{sheet_name}'."
                        )
                        print(f"Error details: {excel_write_error}")
                        print(
                            "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n"
                        )
                        # Continue to try writing other tables
                        continue
                else:
                    # This case implies table_data was empty or only had a header row,
                    # but df creation failed for some other reason (unlikely now)
                    print(
                        f"  DataFrame could not be created for sheet '{sheet_name}'. Skipping sheet creation."
                    )
                    continue  # Skip if df is still None

            print("\nFinished iterating through all collected table data.")
            if written_sheets_count == 0 and len(all_tables_data) > 0:
                print(
                    "Warning: Although data was read, no sheets were successfully written to Excel."
                )
            elif written_sheets_count < len(all_tables_data):
                print(
                    f"Warning: Successfully wrote {written_sheets_count} sheets, but {len(all_tables_data) - written_sheets_count} tables might have failed during processing or writing."
                )

        # This message prints only if the 'with' block completes without outer errors
        print(
            f"\n--- Finished Phase 2: Successfully saved Excel file to: {excel_filepath} ---"
        )

    except ImportError as import_err:
        print(f"Error: Import error occurred: {import_err}")
        print(
            "Please ensure required libraries are installed: pip install python-docx pandas openpyxl lxml tkinter"
        )
    except PermissionError:
        print(f"Error: Permission denied when trying to write to {excel_filepath}.")
        print("Please ensure the file is not open and you have write permissions.")
    except Exception as write_error:
        # General error during writing phase
        print("\n!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print("An unexpected error occurred during Phase 2 (Writing to Excel).")
        print("Traceback:")
        traceback.print_exc()  # Print the full traceback for the phase 2 error
        print(f"Error details: {write_error}")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n")


def select_file_and_run():
    """
    Opens a file dialog for the user to select a Word document,
    starting in the CWD, then runs the table extraction process.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main tkinter window
    try:
        cwd = os.getcwd()
        print(f"Opening file selector in directory: {cwd}")
        input_word_file = filedialog.askopenfilename(
            title="Select Word Document",
            initialdir=cwd,
            filetypes=[("Word Documents", "*.docx"), ("All Files", "*.*")],
        )
        if not input_word_file:
            print("No file selected. Exiting.")
            return
        print(f"File selected: {input_word_file}")
        input_dir = os.path.dirname(input_word_file)
        base_name = os.path.splitext(os.path.basename(input_word_file))[0]
        # Consider adding a timestamp or unique ID if running frequently
        output_excel_file = os.path.join(
            input_dir, f"{base_name}_tables_extracted.xlsx"
        )
        print(f"Output will be saved to: {output_excel_file}")
        extract_tables_to_excel(input_word_file, output_excel_file)
    except Exception as e:
        print(f"An error occurred setting up file paths or running the extraction: {e}")
        traceback.print_exc()
    finally:
        # Ensure tkinter root is destroyed even if errors occur
        try:
            root.destroy()
        except tk.TclError:
            pass  # Handle case where root might already be destroyed


# --- Main Execution ---
if __name__ == "__main__":
    select_file_and_run()
