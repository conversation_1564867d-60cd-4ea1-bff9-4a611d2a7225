import logging
import requests
import pandas as pd
import time
import os
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options

# Configuration Settings
# =========================================

# Credentials
USERNAME = "<EMAIL>"
PASSWORD = "W4h_djbC-fauJM!"

# Date Range Settings
START_DATE = "2024/12/01 00:00:00"  # Format: YYYY/MM/DD HH:MM:SS
END_DATE = "2025/01/01 00:00:00"  # Format: YYYY/MM/DD HH:MM:SS

# Turbine Settings
TURBINE_START_ID = 2307404 + 1  # First turbine ID to fetch
TURBINE_END_ID = 2307404 + 131  # Last turbine ID to fetch

# Request Settings
CHUNK_SIZE_DAYS = 61  # Number of days per request chunk
REQUEST_DELAY = 1  # Delay between API calls in seconds
MAX_RETRIES = 3  # Maximum number of retry attempts
RETRY_BASE_DELAY = 5  # Base delay for exponential backoff in seconds

# Selenium Settings
SELENIUM_TIMEOUT = 300  # Timeout for Selenium operations in seconds
EDGE_DRIVER_PATH = r"C:\Users\<USER>\Documents\Tarec\Siege vs WD\rapports_mensuel\edgedriver_win64\msedgedriver.exe"

# Output Settings
OUTPUT_DIR = "./WD_scrape_output/"  # Directory where Excel files will be saved
OUTPUT_FILE_PREFIX = "wd_data"  # Prefix for output files

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


# Function to log in (using Selenium to get cookies)
def login(driver, username, password):
    logging.info("Navigating to the login page")
    driver.get("https://www.winddialogue.com/performance/global-reporting.aspx")

    # Accept license agreement if it appears
    try:
        logging.info("Checking for license agreement dialog")
        license_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//button[text()='Accept']"))
        )
        logging.info("License agreement dialog appeared, clicking 'Accept'")
        license_button.click()
    except Exception as e:
        logging.info("No license agreement dialog appeared or an error occurred: %s", e)

    # Wait until the form is available and extract the __RequestVerificationToken
    logging.info("Waiting for the __RequestVerificationToken element")
    token_element = WebDriverWait(driver, SELENIUM_TIMEOUT).until(
        EC.presence_of_element_located((By.NAME, "__RequestVerificationToken"))
    )
    token_value = token_element.get_attribute("value")

    # Fill in the login form
    logging.info("Filling in the login form")
    username_input = driver.find_element(By.NAME, "username")
    password_input = driver.find_element(By.NAME, "password")
    username_input.send_keys(username)
    password_input.send_keys(password)

    # Add the token to a hidden input if necessary
    driver.execute_script(
        f"document.getElementsByName('__RequestVerificationToken')[0].value = '{token_value}'"
    )

    # Submit the form
    logging.info("Submitting the login form")
    driver.find_element(By.ID, "btnSignIn").click()

    # Wait for successful login
    WebDriverWait(driver, SELENIUM_TIMEOUT).until(
        EC.presence_of_element_located((By.ID, "sites-table"))
    )
    logging.info("Logged in successfully.")


def get_date_ranges(start_date_str, end_date_str, days_per_chunk=CHUNK_SIZE_DAYS):
    start_date = datetime.strptime(start_date_str, "%Y/%m/%d %H:%M:%S")
    end_date = datetime.strptime(end_date_str, "%Y/%m/%d %H:%M:%S")

    current_date = start_date
    while current_date < end_date:
        chunk_end = min(current_date + timedelta(days=days_per_chunk), end_date)
        yield (
            current_date.strftime("%Y/%m/%d %H:%M:%S"),
            chunk_end.strftime("%Y/%m/%d %H:%M:%S"),
        )
        current_date = chunk_end


def get_production_data(
    session, station_id, start_time, end_time, max_retries=MAX_RETRIES
):
    url = "https://www.winddialogue.com/GlobalReporting/LoadYBAProductionData"
    headers = {
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Referer": "https://www.winddialogue.com/performance/global-reporting.aspx",
        "X-Requested-With": "XMLHttpRequest",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0",
    }
    data = {
        "StationID": station_id,
        "StartTime": start_time,
        "EndTime": end_time,
    }

    for attempt in range(max_retries):
        try:
            logging.info(
                f"Fetching data from {url} with StationID={station_id}, StartTime={start_time}, EndTime={end_time} (Attempt {attempt + 1}/{max_retries})"
            )
            response = session.post(url, headers=headers, data=data)
            response.raise_for_status()
            return response.json()["aaData"]
        except requests.exceptions.ConnectionError as e:
            if "Connection reset" in str(e) and attempt < max_retries - 1:
                retry_delay = (2**attempt) * RETRY_BASE_DELAY
                logging.warning(
                    f"Connection reset. Retrying in {retry_delay} seconds... (Attempt {attempt + 1}/{max_retries})"
                )
                time.sleep(retry_delay)
                continue
            raise
        except Exception as e:
            if attempt < max_retries - 1:
                retry_delay = (2**attempt) * RETRY_BASE_DELAY
                logging.warning(
                    f"Error occurred: {e}. Retrying in {retry_delay} seconds... (Attempt {attempt + 1}/{max_retries})"
                )
                time.sleep(retry_delay)
                continue
            raise


def main():
    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Setup Selenium WebDriver
    service = Service(executable_path=EDGE_DRIVER_PATH)
    edge_options = Options()
    edge_options.add_argument("--no-sandbox")
    edge_options.add_argument("--disable-dev-shm-usage")
    edge_options.add_argument("--disable-features=msEdgeIdentity")
    driver = webdriver.Edge(service=service, options=edge_options)

    try:
        # Login and get cookies
        login(driver, USERNAME, PASSWORD)
        cookies = driver.get_cookies()

        # Create a requests session and set cookies
        session = requests.Session()
        for cookie in cookies:
            session.cookies.set(cookie["name"], cookie["value"])

        all_data = []
        for station_id_num in range(TURBINE_START_ID, TURBINE_END_ID + 1):
            station_id = str(station_id_num)
            logging.info(f"Processing turbine {station_id}")

            # Get data in chunks
            for chunk_start, chunk_end in get_date_ranges(START_DATE, END_DATE):
                logging.info(f"Fetching data for period {chunk_start} to {chunk_end}")
                try:
                    production_data = get_production_data(
                        session, station_id, chunk_start, chunk_end
                    )
                    all_data.extend(production_data)
                except Exception as e:
                    logging.error(f"Error fetching data for turbine {station_id}: {e}")
                    continue
                # Add delay between API calls to prevent overloading
                time.sleep(REQUEST_DELAY)

        # Convert to Pandas DataFrame
        df = pd.DataFrame(all_data)

        # Print or save the DataFrame
        if not df.empty:
            logging.info("Data extraction completed successfully.")
            print(df)
            # Format period for filename
            period_start = datetime.strptime(START_DATE, "%Y/%m/%d %H:%M:%S").strftime(
                "%Y%m%d"
            )
            period_end = datetime.strptime(END_DATE, "%Y/%m/%d %H:%M:%S").strftime(
                "%Y%m%d"
            )

            # Ensure decimal separator is "."
            for column in df.select_dtypes(include=["float64"]).columns:
                df[column] = df[column].apply(lambda x: "{:.2f}".format(x))

            # Save to Excel with period in filename
            filename = f"{OUTPUT_FILE_PREFIX}_{period_start}_to_{period_end}.parquet"
            file_path = os.path.join(OUTPUT_DIR, filename)
            df.to_parquet(file_path, compression="snappy")
            logging.info(f"Data saved to {file_path}")
        else:
            logging.warning("No data was extracted.")

    except Exception as e:
        logging.error("An error occurred: %s", e)
    finally:
        driver.quit()


if __name__ == "__main__":
    main()
