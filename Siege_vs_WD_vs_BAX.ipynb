{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pyodbc\n", "import os\n", "import pandas as pd\n", "from zipfile import ZipFile\n", "import numpy as np\n", "\n", "pd.set_option(\"display.max_columns\", 500)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# mdb to df"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def mdb_to_df(file_name, sql):\n", "\n", "    conn_str = (\n", "        r\"DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};\" fr\"DBQ={file_name};\"\n", "    )\n", "    print(conn_str)\n", "    cnxn = pyodbc.connect(conn_str)\n", "\n", "    df = pd.read_sql(sql, cnxn)\n", "\n", "    cnxn.close()\n", "\n", "    print(f\"{file_name} Loaded\")\n", "    return df\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# CNT read and write"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def read_cnt(file_name):\n", "    usecols_cnt = \"\"\"TimeStamp, StationId, wtc_kWG1TotE_accum, wtc_kWG1TotE_endvalue\"\"\"  # , wtc_kWG1Tot_accum, wtc_kWG1TotE_accum,wtc_kWG1TotI_accum\n", "    # wtc_kWG1TotE_endvalue, wtc_kWG1TotE_accum'''\n", "\n", "    sql_cnt = f\"Select {usecols_cnt} FROM tblSCTurCount;\"\n", "\n", "    cnt = mdb_to_df(file_name=file_name, sql=sql_cnt)\n", "\n", "    cnt[\"TimeStamp\"] = pd.to_datetime(cnt[\"TimeStamp\"], format=\"%m/%d/%y %H:%M:%S\")\n", "\n", "    return cnt\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["['../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-01-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-02-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-03-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-04-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-05-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-06-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-07-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-08-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-09-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-10-cnt.mdb',\n", " '../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-11-cnt.mdb']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["directory = r\"../Availability_Warranty_Dash/monthly_data/uploads/CNT/\"\n", "\n", "arr = [os.path.join(directory, i) for i in os.listdir(directory) if i.endswith(\".mdb\")]\n", "\n", "arr\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-01-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-01-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-01-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-02-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-02-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-02-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-03-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-03-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-03-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-04-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-04-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-04-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-05-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-05-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-05-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-06-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-06-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-06-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-07-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-07-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-07-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-08-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-08-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-08-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-09-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-09-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-09-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-10-cnt.mdb;\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-10-cnt.mdb Loaded\n", "../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-10-cnt.mdb\n", "DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};DBQ=../Availability_Warranty_Dash/monthly_data/uploads/CNT/2021-11-cnt.mdb;\n"]}, {"ename": "Error", "evalue": "('HY000', \"[HY000] [Microsoft][ODBC Microsoft Access Driver] Could not find file '(unknown)'. (-1811) (SQLDriverConnect); [HY000] [Microsoft][ODBC Microsoft Access Driver] Could not find file '(unknown)'. (-1811)\")", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON>r\u001b[0m                                     <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp/ipykernel_14128/259877688.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0mi\u001b[0m \u001b[1;32min\u001b[0m \u001b[0marr\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 4\u001b[1;33m     \u001b[0mdf\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mread_cnt\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mi\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      5\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m     \u001b[0mcnt\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mconcat\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mcnt\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mdf\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\AppData\\Local\\Temp/ipykernel_14128/1403905404.py\u001b[0m in \u001b[0;36mread_cnt\u001b[1;34m(file_name)\u001b[0m\n\u001b[0;32m      5\u001b[0m     \u001b[0msql_cnt\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;34mf\"Select {usecols_cnt} FROM tblSCTurCount;\"\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 7\u001b[1;33m     \u001b[0mcnt\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mmdb_to_df\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mfile_name\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mfile_name\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0msql\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0msql_cnt\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      8\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      9\u001b[0m     cnt['TimeStamp'] = pd.to_datetime(\n", "\u001b[1;32m~\\AppData\\Local\\Temp/ipykernel_14128/524916115.py\u001b[0m in \u001b[0;36mmdb_to_df\u001b[1;34m(file_name, sql)\u001b[0m\n\u001b[0;32m     14\u001b[0m     )\n\u001b[0;32m     15\u001b[0m     \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mconn_str\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 16\u001b[1;33m     \u001b[0mcnxn\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mpyodbc\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mconnect\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mconn_str\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     17\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     18\u001b[0m     \u001b[0mdf\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mread_sql\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0msql\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mcnxn\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mError\u001b[0m: ('HY000', \"[HY000] [Microsoft][ODBC Microsoft Access Driver] Could not find file '(unknown)'. (-1811) (SQLDriverConnect); [HY000] [Microsoft][ODBC Microsoft Access Driver] Could not find file '(unknown)'. (-1811)\")"]}], "source": ["cnt = pd.DataFrame()\n", "\n", "for i in arr:\n", "    df = read_cnt(i)\n", "\n", "    cnt = pd.concat([cnt, df])\n", "\n", "    print(i)\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["cnt[\"StationId\"] = cnt[\"StationId\"] - 2307404\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["cnt.iloc[:, 1:] = cnt.iloc[:, 1:].astype(float)\n", "cnt.iloc[:, 1:] = cnt.iloc[:, 1:].astype(float)\n", "cnt.drop_duplicates(inplace=True)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["full_range = pd.date_range(cnt.TimeStamp.iloc[0], cnt.TimeStamp.iloc[-1], freq=\"10T\")\n", "\n", "cnt_full = cnt.groupby(\"StationId\").apply(\n", "    lambda df: df.set_index(\"TimeStamp\").reindex(index=full_range,)\n", ")\n", "\n", "cnt_full = (\n", "    cnt_full.drop(\"StationId\", axis=1)\n", "    .rename_axis([\"StationId\", \"TimeStamp\"])\n", "    .reset_index()\n", ")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# MAA results"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["cumul_results = pd.DataFrame()\n", "period_year = 2022\n", "\n", "for month in range(4, 5):\n", "\n", "    period_cumul = f\"{str(period_year).zfill(2)}-{str(month).zfill(2)}\"\n", "\n", "    # -------------------------------------------------------------------------\n", "    results = pd.read_pickle(f\"../DATA/results/{period_cumul}.pkl\")\n", "\n", "    results = results[\n", "        [\n", "            \"TimeStamp\",\n", "            \"StationId\",\n", "            \"RealPeriod\",\n", "            \"ELX\",\n", "            \"ELNX\",\n", "            \"Duration 20-25(s)\",\n", "            \"EL_indefini_left\",\n", "            \"wtc_ActPower_max\",\n", "            \"wtc_AcWindSp_mean\",\n", "            \"prev_AcWindSp\",\n", "            \"next_AcWindSp\",\n", "            \"wtc_kWG1TotE_accum\",\n", "            \"Epot\",\n", "            \"wtc_ActPower_min\",\n", "            \"UK Text\",\n", "            \"Duration 115(s)\",\n", "            \"EL_Misassigned\",\n", "            \"EL_2006\",\n", "            \"EL_PowerRed\",\n", "            \"Period 0(s)\",\n", "            \"Period 1(s)\",\n", "        ]\n", "    ]\n", "\n", "    cumul_results = pd.concat([cumul_results, results])\n", "\n", "cumul_results.reset_index(drop=True, inplace=True)\n", "\n", "cumul_results[\"EL_indefini_left\"] = cumul_results[\"EL_indefini_left\"].clip(0)\n", "\n", "cumul_results[\"StationId\"] = cumul_results[\"StationId\"] - 2307404\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['StationId', 'TimeStamp', 'RealPeriod', 'Period 0(s)', 'Period 1(s)',\n", "       'UK Text', 'Duration 2006(s)', 'wtc_kWG1Tot_accum',\n", "       'wtc_kWG1TotE_accum', 'wtc_ActPower_min', 'wtc_ActPower_max',\n", "       'wtc_ActPower_mean', 'Duration 115(s)', 'Duration 20-25(s)',\n", "       'wtc_AcWindSp_mean', 'wtc_ActualWindDirection_mean',\n", "       'met_WindSpeedRot_mean_38', 'met_WindSpeedRot_mean_39',\n", "       'met_WindSpeedRot_mean_246', 'met_WinddirectionRot_mean_38',\n", "       'met_WinddirectionRot_mean_39', 'met_WinddirectionRot_mean_246',\n", "       'wtc_PowerRed_timeon', 'Epot', 'Correction Factor',\n", "       'Available Turbines', 'EL', 'ELX', 'ELNX', 'EL 115', 'EL 20-25',\n", "       'EL_115_left', 'EL_indefini', 'prev_AcWindSp', 'next_AcWindSp',\n", "       'prev_ActPower_min', 'next_ActPower_min', 'prev_Alarme', 'next_Alarme',\n", "       'DiffV1', 'DiffV2', 'EL_PowerRed', 'EL_2006', 'EL_wind',\n", "       'Duration lowind(s)', 'EL_wind_start', 'Duration lowind_start(s)',\n", "       'EL_alarm_start', 'Duration alarm_start(s)', 'EL_indefini_left',\n", "       'EL_Misassigned'],\n", "      dtype='object')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_pickle(f\"../DATA/results/{period_cumul}.pkl\").columns\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "---\n", "---\n", "---\n", "# Analyse\n", "---\n", "---\n", "---\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Consecutive Missing Data"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["cnt_full = (\n", "    cumul_results.merge(cnt_full, how=\"outer\", on=[\"TimeStamp\", \"StationId\"])\n", "    .sort_values([\"StationId\", \"TimeStamp\"])\n", "    .reset_index(drop=True)\n", ")\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# cnt_full['Consecutive'] = (cnt_full.wtc_kWG1TotE_accum_y.groupby((cnt_full.wtc_kWG1TotE_accum_y.fillna(-1) != cnt_full.wtc_kWG1TotE_accum_y.fillna(-1).shift()).cumsum()).transform('size'))\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["cnt_full[\"Consecutive\"] = (\n", "    cnt_full.groupby(\"StationId\")\n", "    .apply(lambda df: df.wtc_kWG1TotE_accum_y.isna().diff().ne(0).cumsum())\n", "    .values\n", ")\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def consecutive_na_production(df):\n", "\n", "    if len(df.query(\"RealPeriod > 0\")) == len(df):\n", "        return None\n", "\n", "    n = df.StationId.iloc[0]\n", "    n_moins_1 = n - 1\n", "    n_plus_1 = n + 1\n", "\n", "    index = pd.Index([(\"\", \"debut\"), (\"\", \"fin\"), (\"\", \"durée\")])\n", "\n", "    index = index.append(\n", "        pd.MultiIndex.from_product(\n", "            [\n", "                [\"turbine n\", \"turbine n_moins_1\", \"turbine n_plus_1\"],\n", "                [\"Ep_restored\", \"Epot\", \"indispo\"],\n", "            ]\n", "        )\n", "    )\n", "\n", "    df1 = pd.DataFrame(columns=index)\n", "\n", "    df1.loc[0, (\"\", \"debut\")] = df.TimeStamp.min()\n", "    df1.loc[0, (\"\", \"fin\")] = df.TimeStamp.max()\n", "    debut = df.TimeStamp.min()\n", "    fin = df.TimeStamp.max()\n", "    df1.loc[0, (\"\", \"durée\")] = (\n", "        df.TimeStamp.max() - df.TimeStamp.min() + pd.<PERSON><PERSON><PERSON>(minutes=10)\n", "    )\n", "\n", "    debut_valid = debut - pd.<PERSON><PERSON><PERSON>(minutes=10)\n", "    fin_valid = fin + pd.<PERSON><PERSON><PERSON>(minutes=10)\n", "\n", "    endvalue = cnt_full.query(\n", "        \"StationId == @n & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "    ).wtc_kWG1TotE_endvalue\n", "\n", "    endvalue_n_moins_1 = cnt_full.query(\n", "        \"StationId == @n_moins_1 & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "    ).wtc_kWG1TotE_endvalue\n", "    endvalue_n_plus_1 = cnt_full.query(\n", "        \"StationId == @n_plus_1 & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "    ).wtc_kWG1TotE_endvalue\n", "\n", "    ELX = df.ELX.sum()\n", "    ELNX = df.ELNX.sum()\n", "\n", "    df1.loc[0, (\"turbine n\", \"Ep_restored\")] = endvalue.iloc[-1] - endvalue.iloc[0]\n", "    df1.loc[0, (\"turbine n\", \"Epot\")] = cnt_full.query(\n", "        \"StationId == @n & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "    ).Epot.sum()\n", "    df1.loc[0, (\"turbine n\", \"indispo\")] = cnt_full.query(\n", "        \"StationId == @n & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "    ).RealPeriod.sum()\n", "    df1.insert(4, (\"turbine n\", \"EL alarmes\"), (ELNX + ELX))\n", "    df1.insert(5, (\"turbine n\", \"20 25 (s)\"), df[\"Duration 20-25(s)\"].sum())\n", "    df1.insert(6, (\"turbine n\", \"EL_indefini_left\"), df[\"EL_indefini_left\"].sum())\n", "\n", "    if n != 1:\n", "        df1.loc[0, (\"turbine n_moins_1\", \"Ep_restored\")] = (\n", "            endvalue_n_moins_1.iloc[-1] - endvalue_n_moins_1.iloc[0]\n", "        )\n", "        df1.loc[0, (\"turbine n_moins_1\", \"Epot\")] = cnt_full.query(\n", "            \"StationId == @n_moins_1 & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "        ).Epot.sum()\n", "        df1.loc[0, (\"turbine n_moins_1\", \"indispo\")] = cnt_full.query(\n", "            \"StationId == @n_moins_1 & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "        ).RealPeriod.sum()\n", "\n", "    if n != 131:\n", "        df1.loc[0, (\"turbine n_plus_1\", \"Ep_restored\")] = (\n", "            endvalue_n_plus_1.iloc[-1] - endvalue_n_plus_1.iloc[0]\n", "        )\n", "        df1.loc[0, (\"turbine n_plus_1\", \"Epot\")] = cnt_full.query(\n", "            \"StationId == @n_plus_1 & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "        ).Epot.sum()\n", "        df1.loc[0, (\"turbine n_plus_1\", \"indispo\")] = cnt_full.query(\n", "            \"StationId == @n_plus_1 & (@debut_valid <= TimeStamp <= @fin_valid)\"\n", "        ).RealPeriod.sum()\n", "\n", "    return df1\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RealPeriod</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>Duration 20-25(s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>wtc_ActPower_max</th>\n", "      <th>wtc_AcWindSp_mean</th>\n", "      <th>prev_AcWindSp</th>\n", "      <th>next_AcWindSp</th>\n", "      <th>wtc_kWG1TotE_accum_x</th>\n", "      <th>Epot</th>\n", "      <th>wtc_ActPower_min</th>\n", "      <th>Duration 115(s)</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "      <th>EL_2006</th>\n", "      <th>EL_PowerRed</th>\n", "      <th>wtc_kWG1TotE_accum_y</th>\n", "      <th>wtc_kWG1TotE_endvalue</th>\n", "      <th>Consecutive</th>\n", "    </tr>\n", "    <tr>\n", "      <th>StationId</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>43.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>336798.56</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>10978.140000</td>\n", "      <td>10955.530000</td>\n", "      <td>0.0</td>\n", "      <td>342758.17</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>15744</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3993.0</td>\n", "      <td>290838.63</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>9699.788873</td>\n", "      <td>9491.446064</td>\n", "      <td>0.0</td>\n", "      <td>298142.79</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>46050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1202.0</td>\n", "      <td>89023.88</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>3236.805239</td>\n", "      <td>3127.187097</td>\n", "      <td>0.0</td>\n", "      <td>90421.93</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2590</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>688.0</td>\n", "      <td>77170.68</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>4050.141192</td>\n", "      <td>2750.319972</td>\n", "      <td>0.0</td>\n", "      <td>114204.04</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1514.0</td>\n", "      <td>61811.67</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>2545.358907</td>\n", "      <td>2592.567045</td>\n", "      <td>0.0</td>\n", "      <td>64578.45</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4064</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>36.0</td>\n", "      <td>48639.31</td>\n", "      <td>12441.0</td>\n", "      <td>20.568101</td>\n", "      <td>1498.224823</td>\n", "      <td>1410.927867</td>\n", "      <td>0.0</td>\n", "      <td>49736.34</td>\n", "      <td>6379.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8942</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>19080.18</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>636.456921</td>\n", "      <td>527.996527</td>\n", "      <td>0.0</td>\n", "      <td>19985.24</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7800.0</td>\n", "      <td>16502.76</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>894.742609</td>\n", "      <td>935.159663</td>\n", "      <td>0.0</td>\n", "      <td>18218.44</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1660</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>12785.48</td>\n", "      <td>864.0</td>\n", "      <td>4.910000</td>\n", "      <td>420.561718</td>\n", "      <td>499.252946</td>\n", "      <td>0.0</td>\n", "      <td>14677.19</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>83.0</td>\n", "      <td>11583.07</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>590.941597</td>\n", "      <td>618.874032</td>\n", "      <td>0.0</td>\n", "      <td>13143.33</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>988</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9059.05</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>681.660000</td>\n", "      <td>660.613085</td>\n", "      <td>0.0</td>\n", "      <td>9652.70</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1704</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8277.99</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>360.390000</td>\n", "      <td>347.440000</td>\n", "      <td>0.0</td>\n", "      <td>8804.33</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>303.0</td>\n", "      <td>6837.26</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>238.080000</td>\n", "      <td>238.730000</td>\n", "      <td>0.0</td>\n", "      <td>7596.45</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>604</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6590.22</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>272.510000</td>\n", "      <td>276.784802</td>\n", "      <td>0.0</td>\n", "      <td>6932.83</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1144</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>72600.0</td>\n", "      <td>6385.78</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>1126.433913</td>\n", "      <td>1110.732925</td>\n", "      <td>0.0</td>\n", "      <td>15312.34</td>\n", "      <td>0.0</td>\n", "      <td>72600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>24.0</td>\n", "      <td>5695.88</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>170.910000</td>\n", "      <td>182.400000</td>\n", "      <td>0.0</td>\n", "      <td>6184.44</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>322</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3495.53</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>183.920000</td>\n", "      <td>179.310000</td>\n", "      <td>0.0</td>\n", "      <td>3495.53</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>642</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>72938.0</td>\n", "      <td>3175.45</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>573.000000</td>\n", "      <td>587.556268</td>\n", "      <td>0.0</td>\n", "      <td>4033.49</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6458</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1956.60</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>73.359561</td>\n", "      <td>63.396100</td>\n", "      <td>0.0</td>\n", "      <td>2481.12</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>600.0</td>\n", "      <td>1750.62</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>60.253643</td>\n", "      <td>89.478838</td>\n", "      <td>0.0</td>\n", "      <td>2736.41</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>146</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3600.0</td>\n", "      <td>1564.80</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>60.400000</td>\n", "      <td>59.220000</td>\n", "      <td>0.0</td>\n", "      <td>2049.34</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>990.34</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>44.372089</td>\n", "      <td>31.979297</td>\n", "      <td>0.0</td>\n", "      <td>1146.65</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>104</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>957.43</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>48.772218</td>\n", "      <td>52.242241</td>\n", "      <td>0.0</td>\n", "      <td>1073.91</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3000.0</td>\n", "      <td>945.26</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>45.145058</td>\n", "      <td>41.249651</td>\n", "      <td>0.0</td>\n", "      <td>1179.35</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>286.0</td>\n", "      <td>706.05</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>62.583058</td>\n", "      <td>55.531133</td>\n", "      <td>0.0</td>\n", "      <td>1302.71</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>126</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>619.16</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>22.412957</td>\n", "      <td>25.148480</td>\n", "      <td>0.0</td>\n", "      <td>619.16</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>531.85</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>25.491882</td>\n", "      <td>26.671234</td>\n", "      <td>0.0</td>\n", "      <td>531.85</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>462.95</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>29.728610</td>\n", "      <td>37.704323</td>\n", "      <td>0.0</td>\n", "      <td>579.43</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>384.88</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>19.419521</td>\n", "      <td>11.829470</td>\n", "      <td>0.0</td>\n", "      <td>548.62</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93.0</th>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>373.03</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>10.302410</td>\n", "      <td>10.163390</td>\n", "      <td>0.0</td>\n", "      <td>528.21</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           RealPeriod  ELX  ELNX  Duration 20-25(s)  EL_indefini_left  \\\n", "StationId                                                               \n", "43.0              0.0  0.0   0.0                0.0         336798.56   \n", "32.0              0.0  0.0   0.0             3993.0         290838.63   \n", "24.0              0.0  0.0   0.0             1202.0          89023.88   \n", "55.0              0.0  0.0   0.0              688.0          77170.68   \n", "96.0              0.0  0.0   0.0             1514.0          61811.67   \n", "31.0              0.0  0.0   0.0               36.0          48639.31   \n", "62.0              0.0  0.0   0.0                0.0          19080.18   \n", "29.0              0.0  0.0   0.0             7800.0          16502.76   \n", "78.0              0.0  0.0   0.0                0.0          12785.48   \n", "85.0              0.0  0.0   0.0               83.0          11583.07   \n", "9.0               0.0  0.0   0.0                0.0           9059.05   \n", "111.0             0.0  0.0   0.0                0.0           8277.99   \n", "92.0              0.0  0.0   0.0              303.0           6837.26   \n", "63.0              0.0  0.0   0.0                0.0           6590.22   \n", "13.0              0.0  0.0   0.0            72600.0           6385.78   \n", "104.0             0.0  0.0   0.0               24.0           5695.88   \n", "47.0              0.0  0.0   0.0                0.0           3495.53   \n", "90.0              0.0  0.0   0.0            72938.0           3175.45   \n", "82.0              0.0  0.0   0.0                0.0           1956.60   \n", "3.0               0.0  0.0   0.0              600.0           1750.62   \n", "128.0             0.0  0.0   0.0             3600.0           1564.80   \n", "48.0              0.0  0.0   0.0                0.0            990.34   \n", "14.0              0.0  0.0   0.0                0.0            957.43   \n", "68.0              0.0  0.0   0.0             3000.0            945.26   \n", "112.0             0.0  0.0   0.0              286.0            706.05   \n", "22.0              0.0  0.0   0.0                0.0            619.16   \n", "30.0              0.0  0.0   0.0                0.0            531.85   \n", "17.0              0.0  0.0   0.0                0.0            462.95   \n", "11.0              0.0  0.0   0.0                0.0            384.88   \n", "93.0              0.0  0.0   0.0                0.0            373.03   \n", "\n", "           wtc_ActPower_max  wtc_AcWindSp_mean  prev_AcWindSp  next_AcWindSp  \\\n", "StationId                                                                      \n", "43.0                    0.0           0.000000   10978.140000   10955.530000   \n", "32.0                    0.0           0.000000    9699.788873    9491.446064   \n", "24.0                    0.0           0.000000    3236.805239    3127.187097   \n", "55.0                    0.0           0.000000    4050.141192    2750.319972   \n", "96.0                    0.0           0.000000    2545.358907    2592.567045   \n", "31.0                12441.0          20.568101    1498.224823    1410.927867   \n", "62.0                    0.0           0.000000     636.456921     527.996527   \n", "29.0                    0.0           0.000000     894.742609     935.159663   \n", "78.0                  864.0           4.910000     420.561718     499.252946   \n", "85.0                    0.0           0.000000     590.941597     618.874032   \n", "9.0                     0.0           0.000000     681.660000     660.613085   \n", "111.0                   0.0           0.000000     360.390000     347.440000   \n", "92.0                    0.0           0.000000     238.080000     238.730000   \n", "63.0                    0.0           0.000000     272.510000     276.784802   \n", "13.0                    0.0           0.000000    1126.433913    1110.732925   \n", "104.0                   0.0           0.000000     170.910000     182.400000   \n", "47.0                    0.0           0.000000     183.920000     179.310000   \n", "90.0                    0.0           0.000000     573.000000     587.556268   \n", "82.0                    0.0           0.000000      73.359561      63.396100   \n", "3.0                     0.0           0.000000      60.253643      89.478838   \n", "128.0                   0.0           0.000000      60.400000      59.220000   \n", "48.0                    0.0           0.000000      44.372089      31.979297   \n", "14.0                    0.0           0.000000      48.772218      52.242241   \n", "68.0                    0.0           0.000000      45.145058      41.249651   \n", "112.0                   0.0           0.000000      62.583058      55.531133   \n", "22.0                    0.0           0.000000      22.412957      25.148480   \n", "30.0                    0.0           0.000000      25.491882      26.671234   \n", "17.0                    0.0           0.000000      29.728610      37.704323   \n", "11.0                    0.0           0.000000      19.419521      11.829470   \n", "93.0                    0.0           0.000000      10.302410      10.163390   \n", "\n", "           wtc_kWG1TotE_accum_x       Epot  wtc_ActPower_min  Duration 115(s)  \\\n", "StationId                                                                       \n", "43.0                        0.0  342758.17               0.0              0.0   \n", "32.0                        0.0  298142.79               0.0              0.0   \n", "24.0                        0.0   90421.93               0.0              0.0   \n", "55.0                        0.0  114204.04               0.0              0.0   \n", "96.0                        0.0   64578.45               0.0              0.0   \n", "31.0                        0.0   49736.34            6379.0              0.0   \n", "62.0                        0.0   19985.24               0.0              0.0   \n", "29.0                        0.0   18218.44               0.0              0.0   \n", "78.0                        0.0   14677.19               1.0              0.0   \n", "85.0                        0.0   13143.33               0.0              0.0   \n", "9.0                         0.0    9652.70               0.0              0.0   \n", "111.0                       0.0    8804.33               0.0              0.0   \n", "92.0                        0.0    7596.45               0.0              0.0   \n", "63.0                        0.0    6932.83               0.0              0.0   \n", "13.0                        0.0   15312.34               0.0          72600.0   \n", "104.0                       0.0    6184.44               0.0              0.0   \n", "47.0                        0.0    3495.53               0.0              0.0   \n", "90.0                        0.0    4033.49               0.0              0.0   \n", "82.0                        0.0    2481.12               0.0              0.0   \n", "3.0                         0.0    2736.41               0.0              0.0   \n", "128.0                       0.0    2049.34               0.0              0.0   \n", "48.0                        0.0    1146.65               0.0              0.0   \n", "14.0                        0.0    1073.91               0.0              0.0   \n", "68.0                        0.0    1179.35               0.0              0.0   \n", "112.0                       0.0    1302.71               0.0              0.0   \n", "22.0                        0.0     619.16               0.0              0.0   \n", "30.0                        0.0     531.85               0.0              0.0   \n", "17.0                        0.0     579.43               0.0              0.0   \n", "11.0                        0.0     548.62               0.0              0.0   \n", "93.0                        0.0     528.21               0.0              0.0   \n", "\n", "           EL_Misassigned  EL_2006  EL_PowerRed  wtc_kWG1TotE_accum_y  \\\n", "StationId                                                               \n", "43.0                  0.0      0.0          0.0                   0.0   \n", "32.0                  0.0      0.0          0.0                   0.0   \n", "24.0                  0.0      0.0          0.0                   0.0   \n", "55.0                  0.0      0.0          0.0                   0.0   \n", "96.0                  0.0      0.0          0.0                   0.0   \n", "31.0                  0.0      0.0          0.0                   0.0   \n", "62.0                  0.0      0.0          0.0                   0.0   \n", "29.0                  0.0      0.0          0.0                   0.0   \n", "78.0                  0.0      0.0          0.0                   0.0   \n", "85.0                  0.0      0.0          0.0                   0.0   \n", "9.0                   0.0      0.0          0.0                   0.0   \n", "111.0                 0.0      0.0          0.0                   0.0   \n", "92.0                  0.0      0.0          0.0                   0.0   \n", "63.0                  0.0      0.0          0.0                   0.0   \n", "13.0                  0.0      0.0          0.0                   0.0   \n", "104.0                 0.0      0.0          0.0                   0.0   \n", "47.0                  0.0      0.0          0.0                   0.0   \n", "90.0                  0.0      0.0          0.0                   0.0   \n", "82.0                  0.0      0.0          0.0                   0.0   \n", "3.0                   0.0      0.0          0.0                   0.0   \n", "128.0                 0.0      0.0          0.0                   0.0   \n", "48.0                  0.0      0.0          0.0                   0.0   \n", "14.0                  0.0      0.0          0.0                   0.0   \n", "68.0                  0.0      0.0          0.0                   0.0   \n", "112.0                 0.0      0.0          0.0                   0.0   \n", "22.0                  0.0      0.0          0.0                   0.0   \n", "30.0                  0.0      0.0          0.0                   0.0   \n", "17.0                  0.0      0.0          0.0                   0.0   \n", "11.0                  0.0      0.0          0.0                   0.0   \n", "93.0                  0.0      0.0          0.0                   0.0   \n", "\n", "           wtc_kWG1TotE_endvalue  Consecutive  \n", "StationId                                      \n", "43.0                         0.0        15744  \n", "32.0                         0.0        46050  \n", "24.0                         0.0         2590  \n", "55.0                         0.0         4290  \n", "96.0                         0.0         4064  \n", "31.0                         0.0         8942  \n", "62.0                         0.0         1016  \n", "29.0                         0.0         1660  \n", "78.0                         0.0         2242  \n", "85.0                         0.0          988  \n", "9.0                          0.0         1704  \n", "111.0                        0.0         1200  \n", "92.0                         0.0          604  \n", "63.0                         0.0         1144  \n", "13.0                         0.0         2184  \n", "104.0                        0.0          322  \n", "47.0                         0.0          642  \n", "90.0                         0.0         6458  \n", "82.0                         0.0           70  \n", "3.0                          0.0          146  \n", "128.0                        0.0           68  \n", "48.0                         0.0          104  \n", "14.0                         0.0           64  \n", "68.0                         0.0           70  \n", "112.0                        0.0          126  \n", "22.0                         0.0           98  \n", "30.0                         0.0           56  \n", "17.0                         0.0           38  \n", "11.0                         0.0           38  \n", "93.0                         0.0           50  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["mask = cnt_full.wtc_kWG1TotE_accum_y.isna()\n", "\n", "cnt_full.loc[mask | mask.shift()].query(\n", "    \"RealPeriod == 0 & wtc_kWG1TotE_accum_y.isna()\"\n", ").groupby(\"StationId\").sum().sort_values(\"EL_indefini_left\", ascending=False).head(30)\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1286057.2399999993, 935547.4775301133)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["cnt_full.EL_indefini_left.sum(), cnt_full.EL_Misassigned.sum()\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>RealPeriod</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>Duration 20-25(s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>wtc_ActPower_max</th>\n", "      <th>wtc_AcWindSp_mean</th>\n", "      <th>prev_AcWindSp</th>\n", "      <th>next_AcWindSp</th>\n", "      <th>wtc_kWG1TotE_accum_x</th>\n", "      <th>Epot</th>\n", "      <th>wtc_ActPower_min</th>\n", "      <th>Duration 115(s)</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "      <th>EL_2006</th>\n", "      <th>EL_PowerRed</th>\n", "      <th>wtc_kWG1TotE_accum_y</th>\n", "      <th>wtc_kWG1TotE_endvalue</th>\n", "      <th>Consecutive</th>\n", "    </tr>\n", "    <tr>\n", "      <th>StationId</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>34.0</th>\n", "      <td>874514.29</td>\n", "      <td>11353.61</td>\n", "      <td>156644.26</td>\n", "      <td>2198235.0</td>\n", "      <td>-0.00</td>\n", "      <td>72025154.0</td>\n", "      <td>355318.77</td>\n", "      <td>344056.23</td>\n", "      <td>346719.48</td>\n", "      <td>9270022.0</td>\n", "      <td>9397428.78</td>\n", "      <td>38770452.0</td>\n", "      <td>793079.0</td>\n", "      <td>2129.42</td>\n", "      <td>0.0</td>\n", "      <td>2390.13</td>\n", "      <td>9270611.0</td>\n", "      <td>3.019994e+12</td>\n", "      <td>737337</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49.0</th>\n", "      <td>1104314.03</td>\n", "      <td>17111.93</td>\n", "      <td>408456.82</td>\n", "      <td>2842550.0</td>\n", "      <td>-0.00</td>\n", "      <td>68046737.0</td>\n", "      <td>343907.88</td>\n", "      <td>353155.14</td>\n", "      <td>349586.57</td>\n", "      <td>8682159.0</td>\n", "      <td>9079455.84</td>\n", "      <td>35754712.0</td>\n", "      <td>1087998.0</td>\n", "      <td>3871.75</td>\n", "      <td>0.0</td>\n", "      <td>2413.22</td>\n", "      <td>8682573.0</td>\n", "      <td>2.996468e+12</td>\n", "      <td>628066</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102.0</th>\n", "      <td>1350093.00</td>\n", "      <td>32257.41</td>\n", "      <td>269352.72</td>\n", "      <td>2658272.0</td>\n", "      <td>-0.00</td>\n", "      <td>65710799.0</td>\n", "      <td>357907.27</td>\n", "      <td>345665.48</td>\n", "      <td>337383.37</td>\n", "      <td>8275381.0</td>\n", "      <td>8573090.17</td>\n", "      <td>33040401.0</td>\n", "      <td>1325395.0</td>\n", "      <td>4538.31</td>\n", "      <td>0.0</td>\n", "      <td>11855.44</td>\n", "      <td>8275731.0</td>\n", "      <td>2.856321e+12</td>\n", "      <td>775933</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26.0</th>\n", "      <td>476661.99</td>\n", "      <td>9251.01</td>\n", "      <td>154722.14</td>\n", "      <td>2000686.0</td>\n", "      <td>-0.00</td>\n", "      <td>71415137.0</td>\n", "      <td>344301.49</td>\n", "      <td>368363.34</td>\n", "      <td>350900.57</td>\n", "      <td>9220943.0</td>\n", "      <td>9347810.10</td>\n", "      <td>38396336.0</td>\n", "      <td>393099.0</td>\n", "      <td>530.79</td>\n", "      <td>0.0</td>\n", "      <td>2201.35</td>\n", "      <td>9221464.0</td>\n", "      <td>3.047596e+12</td>\n", "      <td>485919</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109.0</th>\n", "      <td>948232.86</td>\n", "      <td>30599.84</td>\n", "      <td>193209.41</td>\n", "      <td>2620006.0</td>\n", "      <td>-0.00</td>\n", "      <td>64984894.0</td>\n", "      <td>348697.87</td>\n", "      <td>341608.00</td>\n", "      <td>326979.63</td>\n", "      <td>7948574.0</td>\n", "      <td>8161465.69</td>\n", "      <td>30677530.0</td>\n", "      <td>922573.0</td>\n", "      <td>4343.46</td>\n", "      <td>0.0</td>\n", "      <td>2959.45</td>\n", "      <td>7948908.0</td>\n", "      <td>2.743324e+12</td>\n", "      <td>660192</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96.0</th>\n", "      <td>1066674.00</td>\n", "      <td>15428.60</td>\n", "      <td>329648.62</td>\n", "      <td>2306784.0</td>\n", "      <td>62028.46</td>\n", "      <td>67295352.0</td>\n", "      <td>348403.83</td>\n", "      <td>359795.05</td>\n", "      <td>355862.70</td>\n", "      <td>8637249.0</td>\n", "      <td>9024584.05</td>\n", "      <td>36064533.0</td>\n", "      <td>1070283.0</td>\n", "      <td>1728.84</td>\n", "      <td>0.0</td>\n", "      <td>5388.49</td>\n", "      <td>8637653.0</td>\n", "      <td>2.819190e+12</td>\n", "      <td>573901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55.0</th>\n", "      <td>417578.00</td>\n", "      <td>23494.84</td>\n", "      <td>93157.19</td>\n", "      <td>1989146.0</td>\n", "      <td>77283.54</td>\n", "      <td>73005806.0</td>\n", "      <td>368503.24</td>\n", "      <td>363517.02</td>\n", "      <td>364584.15</td>\n", "      <td>9509100.0</td>\n", "      <td>9705158.52</td>\n", "      <td>40533590.0</td>\n", "      <td>376403.0</td>\n", "      <td>58.21</td>\n", "      <td>0.0</td>\n", "      <td>1447.27</td>\n", "      <td>9509668.0</td>\n", "      <td>3.254384e+12</td>\n", "      <td>617540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24.0</th>\n", "      <td>535846.04</td>\n", "      <td>10770.87</td>\n", "      <td>191959.27</td>\n", "      <td>3234294.0</td>\n", "      <td>90169.32</td>\n", "      <td>71183668.0</td>\n", "      <td>371248.40</td>\n", "      <td>377211.38</td>\n", "      <td>368363.34</td>\n", "      <td>9414177.0</td>\n", "      <td>9672449.45</td>\n", "      <td>40640899.0</td>\n", "      <td>527860.0</td>\n", "      <td>1978.22</td>\n", "      <td>0.0</td>\n", "      <td>2233.04</td>\n", "      <td>9414793.0</td>\n", "      <td>3.135597e+12</td>\n", "      <td>580236</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32.0</th>\n", "      <td>977126.04</td>\n", "      <td>12953.43</td>\n", "      <td>191803.86</td>\n", "      <td>11536602.0</td>\n", "      <td>295232.40</td>\n", "      <td>68445744.0</td>\n", "      <td>348978.71</td>\n", "      <td>350730.66</td>\n", "      <td>344056.23</td>\n", "      <td>8849762.0</td>\n", "      <td>9334682.58</td>\n", "      <td>36724825.0</td>\n", "      <td>975590.0</td>\n", "      <td>3211.02</td>\n", "      <td>0.0</td>\n", "      <td>2762.17</td>\n", "      <td>30445229.0</td>\n", "      <td>2.144325e+12</td>\n", "      <td>1668333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43.0</th>\n", "      <td>795867.58</td>\n", "      <td>14172.94</td>\n", "      <td>183863.25</td>\n", "      <td>3198616.0</td>\n", "      <td>336798.56</td>\n", "      <td>65876868.0</td>\n", "      <td>335258.92</td>\n", "      <td>338816.70</td>\n", "      <td>346648.80</td>\n", "      <td>8272492.0</td>\n", "      <td>8781017.71</td>\n", "      <td>33239458.0</td>\n", "      <td>809903.0</td>\n", "      <td>1824.54</td>\n", "      <td>0.0</td>\n", "      <td>2436.88</td>\n", "      <td>8272942.0</td>\n", "      <td>2.824464e+12</td>\n", "      <td>443326</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>131 rows × 19 columns</p>\n", "</div>"], "text/plain": ["           RealPeriod       ELX       ELNX  Duration 20-25(s)  \\\n", "StationId                                                       \n", "34.0        874514.29  11353.61  156644.26          2198235.0   \n", "49.0       1104314.03  17111.93  408456.82          2842550.0   \n", "102.0      1350093.00  32257.41  269352.72          2658272.0   \n", "26.0        476661.99   9251.01  154722.14          2000686.0   \n", "109.0       948232.86  30599.84  193209.41          2620006.0   \n", "...               ...       ...        ...                ...   \n", "96.0       1066674.00  15428.60  329648.62          2306784.0   \n", "55.0        417578.00  23494.84   93157.19          1989146.0   \n", "24.0        535846.04  10770.87  191959.27          3234294.0   \n", "32.0        977126.04  12953.43  191803.86         11536602.0   \n", "43.0        795867.58  14172.94  183863.25          3198616.0   \n", "\n", "           E<PERSON>_indefini_left  wtc_ActPower_max  wtc_AcWindSp_mean  \\\n", "StationId                                                          \n", "34.0                  -0.00        72025154.0          355318.77   \n", "49.0                  -0.00        68046737.0          343907.88   \n", "102.0                 -0.00        65710799.0          357907.27   \n", "26.0                  -0.00        71415137.0          344301.49   \n", "109.0                 -0.00        64984894.0          348697.87   \n", "...                     ...               ...                ...   \n", "96.0               62028.46        67295352.0          348403.83   \n", "55.0               77283.54        73005806.0          368503.24   \n", "24.0               90169.32        71183668.0          371248.40   \n", "32.0              295232.40        68445744.0          348978.71   \n", "43.0              336798.56        65876868.0          335258.92   \n", "\n", "           prev_AcWindSp  next_AcWindSp  wtc_kWG1TotE_accum_x        Epot  \\\n", "StationId                                                                   \n", "34.0           344056.23      346719.48             9270022.0  9397428.78   \n", "49.0           353155.14      349586.57             8682159.0  9079455.84   \n", "102.0          345665.48      337383.37             8275381.0  8573090.17   \n", "26.0           368363.34      350900.57             9220943.0  9347810.10   \n", "109.0          341608.00      326979.63             7948574.0  8161465.69   \n", "...                  ...            ...                   ...         ...   \n", "96.0           359795.05      355862.70             8637249.0  9024584.05   \n", "55.0           363517.02      364584.15             9509100.0  9705158.52   \n", "24.0           377211.38      368363.34             9414177.0  9672449.45   \n", "32.0           350730.66      344056.23             8849762.0  9334682.58   \n", "43.0           338816.70      346648.80             8272492.0  8781017.71   \n", "\n", "           wtc_ActPower_min  Duration 115(s)  EL_Misassigned  EL_2006  \\\n", "StationId                                                               \n", "34.0             38770452.0         793079.0         2129.42      0.0   \n", "49.0             35754712.0        1087998.0         3871.75      0.0   \n", "102.0            33040401.0        1325395.0         4538.31      0.0   \n", "26.0             38396336.0         393099.0          530.79      0.0   \n", "109.0            30677530.0         922573.0         4343.46      0.0   \n", "...                     ...              ...             ...      ...   \n", "96.0             36064533.0        1070283.0         1728.84      0.0   \n", "55.0             40533590.0         376403.0           58.21      0.0   \n", "24.0             40640899.0         527860.0         1978.22      0.0   \n", "32.0             36724825.0         975590.0         3211.02      0.0   \n", "43.0             33239458.0         809903.0         1824.54      0.0   \n", "\n", "           EL_PowerRed  wtc_kWG1TotE_accum_y  wtc_kWG1TotE_endvalue  \\\n", "StationId                                                             \n", "34.0           2390.13             9270611.0           3.019994e+12   \n", "49.0           2413.22             8682573.0           2.996468e+12   \n", "102.0         11855.44             8275731.0           2.856321e+12   \n", "26.0           2201.35             9221464.0           3.047596e+12   \n", "109.0          2959.45             7948908.0           2.743324e+12   \n", "...                ...                   ...                    ...   \n", "96.0           5388.49             8637653.0           2.819190e+12   \n", "55.0           1447.27             9509668.0           3.254384e+12   \n", "24.0           2233.04             9414793.0           3.135597e+12   \n", "32.0           2762.17            30445229.0           2.144325e+12   \n", "43.0           2436.88             8272942.0           2.824464e+12   \n", "\n", "           Consecutive  \n", "StationId               \n", "34.0            737337  \n", "49.0            628066  \n", "102.0           775933  \n", "26.0            485919  \n", "109.0           660192  \n", "...                ...  \n", "96.0            573901  \n", "55.0            617540  \n", "24.0            580236  \n", "32.0           1668333  \n", "43.0            443326  \n", "\n", "[131 rows x 19 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["cnt_full.groupby(\"StationId\").sum().sort_values(\"EL_indefini_left\").round(2)\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th colspan=\"3\" halign=\"left\"></th>\n", "      <th colspan=\"6\" halign=\"left\">turbine n</th>\n", "      <th colspan=\"3\" halign=\"left\">turbine n_moins_1</th>\n", "      <th colspan=\"3\" halign=\"left\">turbine n_plus_1</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>debut</th>\n", "      <th>fin</th>\n", "      <th>du<PERSON>e</th>\n", "      <th>Ep_restored</th>\n", "      <th>EL alarmes</th>\n", "      <th>20 25 (s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "      <th>Ep_restored</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "      <th>Ep_restored</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Consecutive</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <th>0</th>\n", "      <td>2021-03-04 07:40:00</td>\n", "      <td>2021-03-04 07:40:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>352.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>390.74</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>352.0</td>\n", "      <td>373.74</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <th>0</th>\n", "      <td>2021-06-08 14:00:00</td>\n", "      <td>2021-06-08 17:10:00</td>\n", "      <td>0 days 03:20:00</td>\n", "      <td>7432.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6846.34</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7544.0</td>\n", "      <td>6887.75</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <th>0</th>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>78.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>76.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                          \\\n", "                             debut                  fin            durée   \n", "Consecutive                                                                \n", "2           0  2021-03-04 07:40:00  2021-03-04 07:40:00  0 days 00:10:00   \n", "10          0  2021-06-08 14:00:00  2021-06-08 17:10:00  0 days 03:20:00   \n", "24          0  2021-12-01 00:10:00  2021-12-01 00:10:00  0 days 00:10:00   \n", "\n", "                turbine n                                                 \\\n", "              Ep_restored EL alarmes 20 25 (s) EL_indefini_left     Epot   \n", "Consecutive                                                                \n", "2           0       352.0        0.0       0.0              0.0   390.74   \n", "10          0      7432.0        0.0       0.0              0.0  6846.34   \n", "24          0         NaN        0.0       0.0              0.0     78.0   \n", "\n", "                      turbine n_moins_1              turbine n_plus_1  \\\n", "              indispo       Ep_restored Epot indispo      Ep_restored   \n", "Consecutive                                                             \n", "2           0     0.0               NaN  NaN     NaN            352.0   \n", "10          0     0.0               NaN  NaN     NaN           7544.0   \n", "24          0     0.0               NaN  NaN     NaN              NaN   \n", "\n", "                                \n", "                  Epot indispo  \n", "Consecutive                     \n", "2           0   373.74     0.0  \n", "10          0  6887.75     0.0  \n", "24          0     76.0     0.0  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["cnt_full.query(\"StationId == 1 & wtc_kWG1TotE_accum_y.isna()\").groupby(\n", "    \"Consecutive\"\n", ").apply(consecutive_na_production)\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TimeStamp</th>\n", "      <th>StationId</th>\n", "      <th>RealPeriod</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>Duration 20-25(s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>wtc_ActPower_max</th>\n", "      <th>wtc_AcWindSp_mean</th>\n", "      <th>prev_AcWindSp</th>\n", "      <th>next_AcWindSp</th>\n", "      <th>wtc_kWG1TotE_accum_x</th>\n", "      <th>Epot</th>\n", "      <th>wtc_ActPower_min</th>\n", "      <th>UK Text</th>\n", "      <th>Duration 115(s)</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "      <th>EL_2006</th>\n", "      <th>EL_PowerRed</th>\n", "      <th>wtc_kWG1TotE_accum_y</th>\n", "      <th>wtc_kWG1TotE_endvalue</th>\n", "      <th>Consecutive</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2053601</th>\n", "      <td>2021-08-20 15:40:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>205.19</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>288.0</td>\n", "      <td>0.00</td>\n", "      <td>7.52</td>\n", "      <td>7.60</td>\n", "      <td>0.0</td>\n", "      <td>205.19</td>\n", "      <td>288.0</td>\n", "      <td>CAN:IO-1 Communication error</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>62004740.0</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2053602</th>\n", "      <td>2021-08-20 15:50:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>226.19</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>7.71</td>\n", "      <td>7.79</td>\n", "      <td>0.0</td>\n", "      <td>226.19</td>\n", "      <td>0.0</td>\n", "      <td>CAN:IO-1 Communication error</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2053603</th>\n", "      <td>2021-08-20 16:00:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>239.26</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>7.43</td>\n", "      <td>8.38</td>\n", "      <td>0.0</td>\n", "      <td>239.26</td>\n", "      <td>0.0</td>\n", "      <td>CAN:IO-1 Communication error</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2053604</th>\n", "      <td>2021-08-20 16:10:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>243.37</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>8.45</td>\n", "      <td>8.65</td>\n", "      <td>0.0</td>\n", "      <td>243.37</td>\n", "      <td>0.0</td>\n", "      <td>CAN:IO-1 Communication error|HW-profile not se...</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2053605</th>\n", "      <td>2021-08-20 16:20:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>265.57</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>7.90</td>\n", "      <td>8.40</td>\n", "      <td>0.0</td>\n", "      <td>265.57</td>\n", "      <td>0.0</td>\n", "      <td>HW-profile not selected</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2055361</th>\n", "      <td>2021-09-01 21:00:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>102.51</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>5.88</td>\n", "      <td>6.28</td>\n", "      <td>0.0</td>\n", "      <td>102.51</td>\n", "      <td>0.0</td>\n", "      <td>Stopped for SW update</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2055362</th>\n", "      <td>2021-09-01 21:10:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>98.93</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>5.65</td>\n", "      <td>5.47</td>\n", "      <td>0.0</td>\n", "      <td>98.93</td>\n", "      <td>0.0</td>\n", "      <td>Stopped for SW update</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2055363</th>\n", "      <td>2021-09-01 21:20:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>96.97</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>5.61</td>\n", "      <td>6.32</td>\n", "      <td>0.0</td>\n", "      <td>96.97</td>\n", "      <td>0.0</td>\n", "      <td>Stopped for SW update|HW-profile not selected</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2055364</th>\n", "      <td>2021-09-01 21:30:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>90.18</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "      <td>5.48</td>\n", "      <td>5.30</td>\n", "      <td>0.0</td>\n", "      <td>90.18</td>\n", "      <td>0.0</td>\n", "      <td>HW-profile not selected</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2055365</th>\n", "      <td>2021-09-01 21:40:00</td>\n", "      <td>43.0</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>88.99</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.48</td>\n", "      <td>5.46</td>\n", "      <td>5.44</td>\n", "      <td>0.0</td>\n", "      <td>88.99</td>\n", "      <td>-5.0</td>\n", "      <td>HW-profile not selected|Stopped for SW update|...</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>67375512.0</td>\n", "      <td>13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1765 rows × 22 columns</p>\n", "</div>"], "text/plain": ["                  TimeStamp  StationId  RealPeriod  ELX    ELNX  \\\n", "2053601 2021-08-20 15:40:00       43.0       600.0  0.0  205.19   \n", "2053602 2021-08-20 15:50:00       43.0       600.0  0.0  226.19   \n", "2053603 2021-08-20 16:00:00       43.0       600.0  0.0  239.26   \n", "2053604 2021-08-20 16:10:00       43.0       600.0  0.0  243.37   \n", "2053605 2021-08-20 16:20:00       43.0       600.0  0.0  265.57   \n", "...                     ...        ...         ...  ...     ...   \n", "2055361 2021-09-01 21:00:00       43.0       600.0  0.0  102.51   \n", "2055362 2021-09-01 21:10:00       43.0       600.0  0.0   98.93   \n", "2055363 2021-09-01 21:20:00       43.0       600.0  0.0   96.97   \n", "2055364 2021-09-01 21:30:00       43.0       600.0  0.0   90.18   \n", "2055365 2021-09-01 21:40:00       43.0       600.0  0.0   88.99   \n", "\n", "         Duration 20-25(s)  EL_indefini_left  wtc_ActPower_max  \\\n", "2053601                0.0               0.0             288.0   \n", "2053602                0.0               0.0               0.0   \n", "2053603                0.0               0.0               0.0   \n", "2053604                0.0               0.0               0.0   \n", "2053605                0.0               0.0               0.0   \n", "...                    ...               ...               ...   \n", "2055361              600.0               0.0               0.0   \n", "2055362              600.0               0.0               0.0   \n", "2055363              600.0               0.0               0.0   \n", "2055364              600.0               0.0               0.0   \n", "2055365              600.0               0.0               0.0   \n", "\n", "         wtc_AcWindSp_mean  prev_AcWindSp  next_AcWindSp  \\\n", "2053601               0.00           7.52           7.60   \n", "2053602               0.00           7.71           7.79   \n", "2053603               0.00           7.43           8.38   \n", "2053604               0.00           8.45           8.65   \n", "2053605               0.00           7.90           8.40   \n", "...                    ...            ...            ...   \n", "2055361               0.00           5.88           6.28   \n", "2055362               0.00           5.65           5.47   \n", "2055363               0.00           5.61           6.32   \n", "2055364               0.00           5.48           5.30   \n", "2055365               4.48           5.46           5.44   \n", "\n", "         wtc_kWG1TotE_accum_x    Epot  wtc_ActPower_min  \\\n", "2053601                   0.0  205.19             288.0   \n", "2053602                   0.0  226.19               0.0   \n", "2053603                   0.0  239.26               0.0   \n", "2053604                   0.0  243.37               0.0   \n", "2053605                   0.0  265.57               0.0   \n", "...                       ...     ...               ...   \n", "2055361                   0.0  102.51               0.0   \n", "2055362                   0.0   98.93               0.0   \n", "2055363                   0.0   96.97               0.0   \n", "2055364                   0.0   90.18               0.0   \n", "2055365                   0.0   88.99              -5.0   \n", "\n", "                                                   UK Text  Duration 115(s)  \\\n", "2053601                       CAN:IO-1 Communication error            600.0   \n", "2053602                       CAN:IO-1 Communication error            600.0   \n", "2053603                       CAN:IO-1 Communication error            600.0   \n", "2053604  CAN:IO-1 Communication error|HW-profile not se...            600.0   \n", "2053605                            HW-profile not selected            600.0   \n", "...                                                    ...              ...   \n", "2055361                              Stopped for SW update            600.0   \n", "2055362                              Stopped for SW update            600.0   \n", "2055363      Stopped for SW update|HW-profile not selected            600.0   \n", "2055364                            HW-profile not selected            600.0   \n", "2055365  HW-profile not selected|Stopped for SW update|...            600.0   \n", "\n", "         EL_Misassigned  EL_2006  EL_PowerRed  wtc_kWG1TotE_accum_y  \\\n", "2053601             0.0      0.0          0.0                   0.0   \n", "2053602             0.0      0.0          0.0                   NaN   \n", "2053603             0.0      0.0          0.0                   NaN   \n", "2053604             0.0      0.0          0.0                   NaN   \n", "2053605             0.0      0.0          0.0                   NaN   \n", "...                 ...      ...          ...                   ...   \n", "2055361             0.0      0.0          0.0                   NaN   \n", "2055362             0.0      0.0          0.0                   NaN   \n", "2055363             0.0      0.0          0.0                   NaN   \n", "2055364             0.0      0.0          0.0                   NaN   \n", "2055365             0.0      0.0          0.0                   0.0   \n", "\n", "         wtc_kWG1TotE_endvalue  Consecutive  \n", "2053601             62004740.0           11  \n", "2053602                    NaN           12  \n", "2053603                    NaN           12  \n", "2053604                    NaN           12  \n", "2053605                    NaN           12  \n", "...                        ...          ...  \n", "2055361                    NaN           12  \n", "2055362                    NaN           12  \n", "2055363                    NaN           12  \n", "2055364                    NaN           12  \n", "2055365             67375512.0           13  \n", "\n", "[1765 rows x 22 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["cnt_full.query(\n", "    'StationId == 43 & (\"2021-08-20 15:40:00\" <= TimeStamp <= \"2021-09-01 21:40:00\")'\n", ")\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th colspan=\"3\" halign=\"left\"></th>\n", "      <th colspan=\"6\" halign=\"left\">turbine n</th>\n", "      <th colspan=\"3\" halign=\"left\">turbine n_moins_1</th>\n", "      <th colspan=\"3\" halign=\"left\">turbine n_plus_1</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>debut</th>\n", "      <th>fin</th>\n", "      <th>du<PERSON>e</th>\n", "      <th>Ep_restored</th>\n", "      <th>EL alarmes</th>\n", "      <th>20 25 (s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "      <th>Ep_restored</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "      <th>Ep_restored</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>StationId</th>\n", "      <th>Consecutive</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">1.0</th>\n", "      <th>2</th>\n", "      <th>0</th>\n", "      <td>2021-03-04 07:40:00</td>\n", "      <td>2021-03-04 07:40:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>352.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>390.74</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>352.0</td>\n", "      <td>373.74</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <th>0</th>\n", "      <td>2021-06-08 14:00:00</td>\n", "      <td>2021-06-08 17:10:00</td>\n", "      <td>0 days 03:20:00</td>\n", "      <td>7432.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6846.34</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>7544.0</td>\n", "      <td>6887.75</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <th>0</th>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>78.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>76.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2.0</th>\n", "      <th>2</th>\n", "      <th>0</th>\n", "      <td>2021-03-04 07:40:00</td>\n", "      <td>2021-03-04 07:40:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>352.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>373.74</td>\n", "      <td>0.0</td>\n", "      <td>352.0</td>\n", "      <td>390.74</td>\n", "      <td>0.0</td>\n", "      <td>340.0</td>\n", "      <td>235.74</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <th>0</th>\n", "      <td>2021-06-08 14:00:00</td>\n", "      <td>2021-06-08 17:00:00</td>\n", "      <td>0 days 03:10:00</td>\n", "      <td>7136.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6486.75</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>6452.34</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>7244.73</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">129.0</th>\n", "      <th>6</th>\n", "      <th>0</th>\n", "      <td>2021-03-04 09:50:00</td>\n", "      <td>2021-03-04 09:50:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>272.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>385.29</td>\n", "      <td>0.0</td>\n", "      <td>320.0</td>\n", "      <td>328.29</td>\n", "      <td>0.0</td>\n", "      <td>292.0</td>\n", "      <td>407.29</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <th>0</th>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>59.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>56.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>67.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">130.0</th>\n", "      <th>4</th>\n", "      <th>0</th>\n", "      <td>2021-03-04 09:50:00</td>\n", "      <td>2021-03-04 09:50:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>292.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>407.29</td>\n", "      <td>0.0</td>\n", "      <td>272.0</td>\n", "      <td>385.29</td>\n", "      <td>0.0</td>\n", "      <td>312.0</td>\n", "      <td>282.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <th>0</th>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>67.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>59.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>61.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131.0</th>\n", "      <th>18</th>\n", "      <th>0</th>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>61.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>67.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>362 rows × 15 columns</p>\n", "</div>"], "text/plain": ["                                                                   \\\n", "                                       debut                  fin   \n", "StationId Consecutive                                               \n", "1.0       2           0  2021-03-04 07:40:00  2021-03-04 07:40:00   \n", "          10          0  2021-06-08 14:00:00  2021-06-08 17:10:00   \n", "          24          0  2021-12-01 00:10:00  2021-12-01 00:10:00   \n", "2.0       2           0  2021-03-04 07:40:00  2021-03-04 07:40:00   \n", "          12          0  2021-06-08 14:00:00  2021-06-08 17:00:00   \n", "...                                      ...                  ...   \n", "129.0     6           0  2021-03-04 09:50:00  2021-03-04 09:50:00   \n", "          20          0  2021-12-01 00:10:00  2021-12-01 00:10:00   \n", "130.0     4           0  2021-03-04 09:50:00  2021-03-04 09:50:00   \n", "          20          0  2021-12-01 00:10:00  2021-12-01 00:10:00   \n", "131.0     18          0  2021-12-01 00:10:00  2021-12-01 00:10:00   \n", "\n", "                                           turbine n                       \\\n", "                                   <PERSON><PERSON><PERSON> Ep_restored EL alarmes 20 25 (s)   \n", "StationId Consecutive                                                       \n", "1.0       2           0  0 days 00:10:00       352.0        0.0       0.0   \n", "          10          0  0 days 03:20:00      7432.0        0.0       0.0   \n", "          24          0  0 days 00:10:00         NaN        0.0       0.0   \n", "2.0       2           0  0 days 00:10:00       352.0        0.0       0.0   \n", "          12          0  0 days 03:10:00      7136.0        0.0       0.0   \n", "...                                  ...         ...        ...       ...   \n", "129.0     6           0  0 days 00:10:00       272.0        0.0       0.0   \n", "          20          0  0 days 00:10:00         NaN        0.0       0.0   \n", "130.0     4           0  0 days 00:10:00       292.0        0.0       0.0   \n", "          20          0  0 days 00:10:00         NaN        0.0       0.0   \n", "131.0     18          0  0 days 00:10:00         NaN        0.0       0.0   \n", "\n", "                                                          turbine n_moins_1  \\\n", "                        E<PERSON>_indefini_left     Epot indispo       Ep_restored   \n", "StationId Consecutive                                                         \n", "1.0       2           0              0.0   390.74     0.0               NaN   \n", "          10          0              0.0  6846.34     0.0               NaN   \n", "          24          0              0.0     78.0     0.0               NaN   \n", "2.0       2           0              0.0   373.74     0.0             352.0   \n", "          12          0              0.0  6486.75     0.0               NaN   \n", "...                                  ...      ...     ...               ...   \n", "129.0     6           0              0.0   385.29     0.0             320.0   \n", "          20          0              0.0     59.0     0.0               NaN   \n", "130.0     4           0              0.0   407.29     0.0             272.0   \n", "          20          0              0.0     67.0     0.0               NaN   \n", "131.0     18          0              0.0     61.0     0.0               NaN   \n", "\n", "                                         turbine n_plus_1                   \n", "                            Epot indispo      Ep_restored     Epot indispo  \n", "StationId Consecutive                                                       \n", "1.0       2           0      NaN     NaN            352.0   373.74     0.0  \n", "          10          0      NaN     NaN           7544.0  6887.75     0.0  \n", "          24          0      NaN     NaN              NaN     76.0     0.0  \n", "2.0       2           0   390.74     0.0            340.0   235.74     0.0  \n", "          12          0  6452.34     0.0              NaN  7244.73     0.0  \n", "...                          ...     ...              ...      ...     ...  \n", "129.0     6           0   328.29     0.0            292.0   407.29     0.0  \n", "          20          0     56.0     0.0              NaN     67.0     0.0  \n", "130.0     4           0   385.29     0.0            312.0    282.0     0.0  \n", "          20          0     59.0     0.0              NaN     61.0     0.0  \n", "131.0     18          0     67.0     0.0              NaN      NaN     NaN  \n", "\n", "[362 rows x 15 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["missing_data_check = (\n", "    cnt_full.query(\"wtc_kWG1TotE_accum_y.isna()\")\n", "    .groupby([\"StationId\", \"Consecutive\"])\n", "    .apply(consecutive_na_production)\n", ")\n", "\n", "missing_data_check\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th colspan=\"3\" halign=\"left\"></th>\n", "      <th colspan=\"6\" halign=\"left\">turbine n</th>\n", "      <th colspan=\"3\" halign=\"left\">turbine n_moins_1</th>\n", "      <th colspan=\"3\" halign=\"left\">turbine n_plus_1</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>debut</th>\n", "      <th>fin</th>\n", "      <th>du<PERSON>e</th>\n", "      <th>Ep_restored</th>\n", "      <th>EL alarmes</th>\n", "      <th>20 25 (s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "      <th>Ep_restored</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "      <th>Ep_restored</th>\n", "      <th>Epot</th>\n", "      <th>indispo</th>\n", "    </tr>\n", "    <tr>\n", "      <th>StationId</th>\n", "      <th>Consecutive</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>43.0</th>\n", "      <th>12</th>\n", "      <th>0</th>\n", "      <td>2021-08-20 15:50:00</td>\n", "      <td>2021-09-01 21:30:00</td>\n", "      <td>12 days 05:50:00</td>\n", "      <td>5370772.0</td>\n", "      <td>72235.47</td>\n", "      <td>191327.0</td>\n", "      <td>336798.56</td>\n", "      <td>415287.82</td>\n", "      <td>271405.59</td>\n", "      <td>398000.0</td>\n", "      <td>398958.27</td>\n", "      <td>420.0</td>\n", "      <td>406328.0</td>\n", "      <td>417650.99</td>\n", "      <td>19680.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32.0</th>\n", "      <th>42</th>\n", "      <th>0</th>\n", "      <td>2021-06-08 13:10:00</td>\n", "      <td>2021-06-16 06:40:00</td>\n", "      <td>7 days 17:40:00</td>\n", "      <td>305312.0</td>\n", "      <td>4800.98</td>\n", "      <td>11818.0</td>\n", "      <td>290838.63</td>\n", "      <td>303109.38</td>\n", "      <td>9753.0</td>\n", "      <td>306880.0</td>\n", "      <td>309526.9</td>\n", "      <td>5164.0</td>\n", "      <td>311280.0</td>\n", "      <td>312045.64</td>\n", "      <td>209.999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24.0</th>\n", "      <th>8</th>\n", "      <th>0</th>\n", "      <td>2021-06-07 03:40:00</td>\n", "      <td>2021-06-10 10:40:00</td>\n", "      <td>3 days 07:10:00</td>\n", "      <td>94744.0</td>\n", "      <td>22750.18</td>\n", "      <td>93137.0</td>\n", "      <td>88902.42</td>\n", "      <td>113488.63</td>\n", "      <td>91145.999</td>\n", "      <td>121860.0</td>\n", "      <td>122023.98</td>\n", "      <td>32015.999</td>\n", "      <td>118268.0</td>\n", "      <td>118544.34</td>\n", "      <td>32015.999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55.0</th>\n", "      <th>10</th>\n", "      <th>0</th>\n", "      <td>2021-06-07 04:20:00</td>\n", "      <td>2021-06-10 11:20:00</td>\n", "      <td>3 days 07:10:00</td>\n", "      <td>128464.0</td>\n", "      <td>632.25</td>\n", "      <td>31546.0</td>\n", "      <td>76872.81</td>\n", "      <td>114711.76</td>\n", "      <td>31019.999</td>\n", "      <td>NaN</td>\n", "      <td>127499.19</td>\n", "      <td>30307.999</td>\n", "      <td>124248.0</td>\n", "      <td>121350.38</td>\n", "      <td>30307.999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96.0</th>\n", "      <th>12</th>\n", "      <th>0</th>\n", "      <td>2021-05-29 02:00:00</td>\n", "      <td>2021-05-31 14:00:00</td>\n", "      <td>2 days 12:10:00</td>\n", "      <td>64360.0</td>\n", "      <td>4644.89</td>\n", "      <td>16192.0</td>\n", "      <td>61811.67</td>\n", "      <td>69405.73</td>\n", "      <td>14606.003</td>\n", "      <td>69216.0</td>\n", "      <td>71335.46</td>\n", "      <td>7498.0</td>\n", "      <td>66940.0</td>\n", "      <td>68709.56</td>\n", "      <td>7162.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31.0</th>\n", "      <th>72</th>\n", "      <th>0</th>\n", "      <td>2021-07-25 18:10:00</td>\n", "      <td>2021-07-26 10:20:00</td>\n", "      <td>0 days 16:20:00</td>\n", "      <td>37064.0</td>\n", "      <td>765.04</td>\n", "      <td>975.0</td>\n", "      <td>35134.27</td>\n", "      <td>37086.56</td>\n", "      <td>803.0</td>\n", "      <td>38492.0</td>\n", "      <td>38911.25</td>\n", "      <td>0.0</td>\n", "      <td>38312.0</td>\n", "      <td>38786.64</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62.0</th>\n", "      <th>16</th>\n", "      <th>0</th>\n", "      <td>2021-06-08 14:00:00</td>\n", "      <td>2021-06-09 00:00:00</td>\n", "      <td>0 days 10:10:00</td>\n", "      <td>21336.0</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>19080.18</td>\n", "      <td>20434.33</td>\n", "      <td>0.0</td>\n", "      <td>21056.0</td>\n", "      <td>21388.0</td>\n", "      <td>0.0</td>\n", "      <td>8500.0</td>\n", "      <td>20832.14</td>\n", "      <td>19595.997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29.0</th>\n", "      <th>10</th>\n", "      <th>0</th>\n", "      <td>2021-03-04 13:50:00</td>\n", "      <td>2021-03-05 12:30:00</td>\n", "      <td>0 days 22:50:00</td>\n", "      <td>12828.0</td>\n", "      <td>413.28</td>\n", "      <td>1838.0</td>\n", "      <td>12938.65</td>\n", "      <td>14959.94</td>\n", "      <td>1909.998</td>\n", "      <td>13852.0</td>\n", "      <td>14374.78</td>\n", "      <td>209.999</td>\n", "      <td>14000.0</td>\n", "      <td>14342.98</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85.0</th>\n", "      <th>10</th>\n", "      <th>0</th>\n", "      <td>2021-03-16 17:20:00</td>\n", "      <td>2021-03-17 13:00:00</td>\n", "      <td>0 days 19:50:00</td>\n", "      <td>12376.0</td>\n", "      <td>3241.85</td>\n", "      <td>14933.0</td>\n", "      <td>11419.33</td>\n", "      <td>16283.59</td>\n", "      <td>15283.001</td>\n", "      <td>15580.0</td>\n", "      <td>15866.0</td>\n", "      <td>0.0</td>\n", "      <td>15540.0</td>\n", "      <td>15779.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78.0</th>\n", "      <th>40</th>\n", "      <th>0</th>\n", "      <td>2021-08-19 01:30:00</td>\n", "      <td>2021-08-19 09:40:00</td>\n", "      <td>0 days 08:20:00</td>\n", "      <td>12520.0</td>\n", "      <td>903.70</td>\n", "      <td>967.0</td>\n", "      <td>10667.94</td>\n", "      <td>13883.59</td>\n", "      <td>759.0</td>\n", "      <td>13548.0</td>\n", "      <td>14272.34</td>\n", "      <td>86.0</td>\n", "      <td>13912.0</td>\n", "      <td>14263.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9.0</th>\n", "      <th>16</th>\n", "      <th>0</th>\n", "      <td>2021-09-01 13:40:00</td>\n", "      <td>2021-09-02 09:40:00</td>\n", "      <td>0 days 20:10:00</td>\n", "      <td>11064.0</td>\n", "      <td>2690.97</td>\n", "      <td>9740.0</td>\n", "      <td>8946.02</td>\n", "      <td>12393.39</td>\n", "      <td>10222.0</td>\n", "      <td>13744.0</td>\n", "      <td>13921.56</td>\n", "      <td>0.0</td>\n", "      <td>13408.0</td>\n", "      <td>13810.41</td>\n", "      <td>884.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111.0</th>\n", "      <th>22</th>\n", "      <th>0</th>\n", "      <td>2021-10-13 09:40:00</td>\n", "      <td>2021-10-13 22:20:00</td>\n", "      <td>0 days 12:50:00</td>\n", "      <td>6260.0</td>\n", "      <td>6979.64</td>\n", "      <td>21360.0</td>\n", "      <td>7321.89</td>\n", "      <td>14760.37</td>\n", "      <td>21826.0</td>\n", "      <td>12920.0</td>\n", "      <td>12993.0</td>\n", "      <td>0.0</td>\n", "      <td>14020.0</td>\n", "      <td>14154.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63.0</th>\n", "      <th>30</th>\n", "      <th>0</th>\n", "      <td>2021-11-05 03:30:00</td>\n", "      <td>2021-11-05 10:30:00</td>\n", "      <td>0 days 07:10:00</td>\n", "      <td>6620.0</td>\n", "      <td>1611.51</td>\n", "      <td>2293.0</td>\n", "      <td>6590.22</td>\n", "      <td>8856.4</td>\n", "      <td>2025.0</td>\n", "      <td>8096.0</td>\n", "      <td>8345.89</td>\n", "      <td>364.0</td>\n", "      <td>8108.0</td>\n", "      <td>8284.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92.0</th>\n", "      <th>30</th>\n", "      <th>0</th>\n", "      <td>2021-08-27 09:50:00</td>\n", "      <td>2021-08-27 14:40:00</td>\n", "      <td>0 days 05:00:00</td>\n", "      <td>7072.0</td>\n", "      <td>4382.79</td>\n", "      <td>6838.0</td>\n", "      <td>6239.45</td>\n", "      <td>11707.2</td>\n", "      <td>7853.0</td>\n", "      <td>12016.0</td>\n", "      <td>12371.0</td>\n", "      <td>0.0</td>\n", "      <td>12048.0</td>\n", "      <td>12436.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13.0</th>\n", "      <th>18</th>\n", "      <th>0</th>\n", "      <td>2021-09-23 09:50:00</td>\n", "      <td>2021-09-24 09:30:00</td>\n", "      <td>0 days 23:50:00</td>\n", "      <td>9112.0</td>\n", "      <td>2450.43</td>\n", "      <td>22560.0</td>\n", "      <td>6036.20</td>\n", "      <td>8826.26</td>\n", "      <td>23603.0</td>\n", "      <td>9928.0</td>\n", "      <td>11692.89</td>\n", "      <td>18951.0</td>\n", "      <td>9552.0</td>\n", "      <td>11729.09</td>\n", "      <td>21252.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                   \\\n", "                                       debut                  fin   \n", "StationId Consecutive                                               \n", "43.0      12          0  2021-08-20 15:50:00  2021-09-01 21:30:00   \n", "32.0      42          0  2021-06-08 13:10:00  2021-06-16 06:40:00   \n", "24.0      8           0  2021-06-07 03:40:00  2021-06-10 10:40:00   \n", "55.0      10          0  2021-06-07 04:20:00  2021-06-10 11:20:00   \n", "96.0      12          0  2021-05-29 02:00:00  2021-05-31 14:00:00   \n", "31.0      72          0  2021-07-25 18:10:00  2021-07-26 10:20:00   \n", "62.0      16          0  2021-06-08 14:00:00  2021-06-09 00:00:00   \n", "29.0      10          0  2021-03-04 13:50:00  2021-03-05 12:30:00   \n", "85.0      10          0  2021-03-16 17:20:00  2021-03-17 13:00:00   \n", "78.0      40          0  2021-08-19 01:30:00  2021-08-19 09:40:00   \n", "9.0       16          0  2021-09-01 13:40:00  2021-09-02 09:40:00   \n", "111.0     22          0  2021-10-13 09:40:00  2021-10-13 22:20:00   \n", "63.0      30          0  2021-11-05 03:30:00  2021-11-05 10:30:00   \n", "92.0      30          0  2021-08-27 09:50:00  2021-08-27 14:40:00   \n", "13.0      18          0  2021-09-23 09:50:00  2021-09-24 09:30:00   \n", "\n", "                                            turbine n                       \\\n", "                                    <PERSON><PERSON><PERSON> Ep_restored EL alarmes 20 25 (s)   \n", "StationId Consecutive                                                        \n", "43.0      12          0  12 days 05:50:00   5370772.0   72235.47  191327.0   \n", "32.0      42          0   7 days 17:40:00    305312.0    4800.98   11818.0   \n", "24.0      8           0   3 days 07:10:00     94744.0   22750.18   93137.0   \n", "55.0      10          0   3 days 07:10:00    128464.0     632.25   31546.0   \n", "96.0      12          0   2 days 12:10:00     64360.0    4644.89   16192.0   \n", "31.0      72          0   0 days 16:20:00     37064.0     765.04     975.0   \n", "62.0      16          0   0 days 10:10:00     21336.0       0.00       0.0   \n", "29.0      10          0   0 days 22:50:00     12828.0     413.28    1838.0   \n", "85.0      10          0   0 days 19:50:00     12376.0    3241.85   14933.0   \n", "78.0      40          0   0 days 08:20:00     12520.0     903.70     967.0   \n", "9.0       16          0   0 days 20:10:00     11064.0    2690.97    9740.0   \n", "111.0     22          0   0 days 12:50:00      6260.0    6979.64   21360.0   \n", "63.0      30          0   0 days 07:10:00      6620.0    1611.51    2293.0   \n", "92.0      30          0   0 days 05:00:00      7072.0    4382.79    6838.0   \n", "13.0      18          0   0 days 23:50:00      9112.0    2450.43   22560.0   \n", "\n", "                                                                \\\n", "                        EL_indefini_left       Epot    indispo   \n", "StationId Consecutive                                            \n", "43.0      12          0        336798.56  415287.82  271405.59   \n", "32.0      42          0        290838.63  303109.38     9753.0   \n", "24.0      8           0         88902.42  113488.63  91145.999   \n", "55.0      10          0         76872.81  114711.76  31019.999   \n", "96.0      12          0         61811.67   69405.73  14606.003   \n", "31.0      72          0         35134.27   37086.56      803.0   \n", "62.0      16          0         19080.18   20434.33        0.0   \n", "29.0      10          0         12938.65   14959.94   1909.998   \n", "85.0      10          0         11419.33   16283.59  15283.001   \n", "78.0      40          0         10667.94   13883.59      759.0   \n", "9.0       16          0          8946.02   12393.39    10222.0   \n", "111.0     22          0          7321.89   14760.37    21826.0   \n", "63.0      30          0          6590.22     8856.4     2025.0   \n", "92.0      30          0          6239.45    11707.2     7853.0   \n", "13.0      18          0          6036.20    8826.26    23603.0   \n", "\n", "                        turbine n_moins_1                        \\\n", "                              Ep_restored       Epot    indispo   \n", "StationId Consecutive                                             \n", "43.0      12          0          398000.0  398958.27      420.0   \n", "32.0      42          0          306880.0   309526.9     5164.0   \n", "24.0      8           0          121860.0  122023.98  32015.999   \n", "55.0      10          0               NaN  127499.19  30307.999   \n", "96.0      12          0           69216.0   71335.46     7498.0   \n", "31.0      72          0           38492.0   38911.25        0.0   \n", "62.0      16          0           21056.0    21388.0        0.0   \n", "29.0      10          0           13852.0   14374.78    209.999   \n", "85.0      10          0           15580.0    15866.0        0.0   \n", "78.0      40          0           13548.0   14272.34       86.0   \n", "9.0       16          0           13744.0   13921.56        0.0   \n", "111.0     22          0           12920.0    12993.0        0.0   \n", "63.0      30          0            8096.0    8345.89      364.0   \n", "92.0      30          0           12016.0    12371.0        0.0   \n", "13.0      18          0            9928.0   11692.89    18951.0   \n", "\n", "                        turbine n_plus_1                        \n", "                             Ep_restored       Epot    indispo  \n", "StationId Consecutive                                           \n", "43.0      12          0         406328.0  417650.99    19680.0  \n", "32.0      42          0         311280.0  312045.64    209.999  \n", "24.0      8           0         118268.0  118544.34  32015.999  \n", "55.0      10          0         124248.0  121350.38  30307.999  \n", "96.0      12          0          66940.0   68709.56     7162.0  \n", "31.0      72          0          38312.0   38786.64        0.0  \n", "62.0      16          0           8500.0   20832.14  19595.997  \n", "29.0      10          0          14000.0   14342.98        0.0  \n", "85.0      10          0          15540.0    15779.0        0.0  \n", "78.0      40          0          13912.0    14263.0        0.0  \n", "9.0       16          0          13408.0   13810.41      884.0  \n", "111.0     22          0          14020.0    14154.0        0.0  \n", "63.0      30          0           8108.0     8284.0        0.0  \n", "92.0      30          0          12048.0    12436.0        0.0  \n", "13.0      18          0           9552.0   11729.09    21252.0  "]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["missing_data_check.sort_values(\n", "    ((\"turbine n\", \"EL_indefini_left\")), ascending=False\n", ").head(15)\n", "\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"ename": "UndefinedVariableError", "evalue": "name 'EL_indefini_left' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\scope.py\u001b[0m in \u001b[0;36mresolve\u001b[1;34m(self, key, is_local)\u001b[0m\n\u001b[0;32m    199\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mhas_resolvers\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 200\u001b[1;33m                 \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mresolvers\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mkey\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    201\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\collections\\__init__.py\u001b[0m in \u001b[0;36m__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m    940\u001b[0m                 \u001b[1;32mpass\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 941\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__missing__\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[1;33m)\u001b[0m            \u001b[1;31m# support subclasses that define __missing__\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    942\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\collections\\__init__.py\u001b[0m in \u001b[0;36m__missing__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m    932\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m__missing__\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mkey\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 933\u001b[1;33m         \u001b[1;32mraise\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    934\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON>Error\u001b[0m: 'EL_indefini_left'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\scope.py\u001b[0m in \u001b[0;36mresolve\u001b[1;34m(self, key, is_local)\u001b[0m\n\u001b[0;32m    210\u001b[0m                 \u001b[1;31m# e.g., df[df > 0]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 211\u001b[1;33m                 \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtemps\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mkey\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    212\u001b[0m             \u001b[1;32mexcept\u001b[0m \u001b[0mKeyError\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0merr\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON>Error\u001b[0m: 'EL_indefini_left'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mUndefinedVariableError\u001b[0m                    <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp/ipykernel_7432/3889841913.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[1;32m----> 1\u001b[1;33m \u001b[0mmissing_data_check\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mquery\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"EL_indefini_left > 0\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msort_values\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m''\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m'durée'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mascending\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mFalse\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mhead\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;36m15\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\frame.py\u001b[0m in \u001b[0;36mquery\u001b[1;34m(self, expr, inplace, **kwargs)\u001b[0m\n\u001b[0;32m   4058\u001b[0m         \u001b[0mkwargs\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m\"level\"\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mpop\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"level\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;36m0\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m+\u001b[0m \u001b[1;36m1\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   4059\u001b[0m         \u001b[0mkwargs\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m\"target\"\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 4060\u001b[1;33m         \u001b[0mres\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0meval\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mexpr\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   4061\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   4062\u001b[0m         \u001b[1;32mtry\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\frame.py\u001b[0m in \u001b[0;36meval\u001b[1;34m(self, expr, inplace, **kwargs)\u001b[0m\n\u001b[0;32m   4189\u001b[0m         \u001b[0mkwargs\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;34m\"resolvers\"\u001b[0m\u001b[1;33m]\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"resolvers\"\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m+\u001b[0m \u001b[0mtuple\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mresolvers\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   4190\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 4191\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0m_eval\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mexpr\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0minplace\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0minplace\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   4192\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   4193\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mselect_dtypes\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0minclude\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mexclude\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mDataFrame\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\eval.py\u001b[0m in \u001b[0;36meval\u001b[1;34m(expr, parser, engine, truediv, local_dict, global_dict, resolvers, level, target, inplace)\u001b[0m\n\u001b[0;32m    346\u001b[0m         )\n\u001b[0;32m    347\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 348\u001b[1;33m         \u001b[0mparsed_expr\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mExpr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mexpr\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mengine\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mengine\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mparser\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mparser\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0menv\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    349\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    350\u001b[0m         \u001b[1;31m# construct the engine and evaluate the parsed expression\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36m__init__\u001b[1;34m(self, expr, engine, parser, env, level)\u001b[0m\n\u001b[0;32m    804\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mparser\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mparser\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    805\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_visitor\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mPARSERS\u001b[0m\u001b[1;33m[\u001b[0m\u001b[0mparser\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mparser\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 806\u001b[1;33m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mterms\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mparse\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    807\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    808\u001b[0m     \u001b[1;33m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mparse\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    823\u001b[0m         \u001b[0mParse\u001b[0m \u001b[0man\u001b[0m \u001b[0mexpression\u001b[0m\u001b[1;33m.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    824\u001b[0m         \"\"\"\n\u001b[1;32m--> 825\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_visitor\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mexpr\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    826\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    827\u001b[0m     \u001b[1;33m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;34m\"visit_\"\u001b[0m \u001b[1;33m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 411\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    412\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    413\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit_Module\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    415\u001b[0m             \u001b[1;32mraise\u001b[0m \u001b[0mSyntaxError\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"only a single expression is allowed\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    416\u001b[0m         \u001b[0mexpr\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbody\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 417\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mexpr\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    418\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    419\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_Expr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;34m\"visit_\"\u001b[0m \u001b[1;33m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 411\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    412\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    413\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit_Expr\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    418\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    419\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_Expr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 420\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvalue\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    421\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    422\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_rewrite_membership_op\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mright\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;34m\"visit_\"\u001b[0m \u001b[1;33m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 411\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    412\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    413\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit_Compare\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    716\u001b[0m             \u001b[0mop\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtranslate_In\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mops\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    717\u001b[0m             \u001b[0mbinop\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mast\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mBinOp\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mop\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mop\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mleft\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mright\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mcomps\u001b[0m\u001b[1;33m[\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 718\u001b[1;33m             \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mbinop\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    719\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    720\u001b[0m         \u001b[1;31m# recursive case: we have a chained comparison, a CMP b CMP c, etc.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;34m\"visit_\"\u001b[0m \u001b[1;33m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 411\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    412\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    413\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit_BinOp\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    530\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    531\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_BinOp\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 532\u001b[1;33m         \u001b[0mop\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mop_class\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mright\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_maybe_transform_eq_ne\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    533\u001b[0m         \u001b[0mleft\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mright\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_maybe_downcast_constants\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mleft\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mright\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    534\u001b[0m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_maybe_evaluate_binop\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mop\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mop_class\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mright\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36m_maybe_transform_eq_ne\u001b[1;34m(self, node, left, right)\u001b[0m\n\u001b[0;32m    450\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_maybe_transform_eq_ne\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mright\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    451\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mleft\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 452\u001b[1;33m             \u001b[0mleft\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mleft\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mside\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;34m\"left\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    453\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mright\u001b[0m \u001b[1;32mis\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    454\u001b[0m             \u001b[0mright\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mright\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mside\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;34m\"right\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;34m\"visit_\"\u001b[0m \u001b[1;33m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 411\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    412\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    413\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\expr.py\u001b[0m in \u001b[0;36mvisit_Name\u001b[1;34m(self, node, **kwargs)\u001b[0m\n\u001b[0;32m    543\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    544\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_Name\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 545\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mterm_type\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mid\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    546\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    547\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mvisit_NameConstant\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\ops.py\u001b[0m in \u001b[0;36m__init__\u001b[1;34m(self, name, env, side, encoding)\u001b[0m\n\u001b[0;32m     96\u001b[0m         \u001b[0mtname\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mstr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mname\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     97\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mis_local\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mtname\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mstartswith\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mLOCAL_TAG\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mor\u001b[0m \u001b[0mtname\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mDEFAULT_GLOBALS\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 98\u001b[1;33m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_value\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_resolve_name\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     99\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mencoding\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mencoding\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    100\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\ops.py\u001b[0m in \u001b[0;36m_resolve_name\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    113\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    114\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_resolve_name\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 115\u001b[1;33m         \u001b[0mres\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mresolve\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mlocal_name\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mis_local\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mis_local\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    116\u001b[0m         \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mupdate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mres\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    117\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\miniconda3\\lib\\site-packages\\pandas\\core\\computation\\scope.py\u001b[0m in \u001b[0;36mresolve\u001b[1;34m(self, key, is_local)\u001b[0m\n\u001b[0;32m    214\u001b[0m                 \u001b[1;32mfrom\u001b[0m \u001b[0mpandas\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcore\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcomputation\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mops\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mUndefinedVariableError\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    215\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 216\u001b[1;33m                 \u001b[1;32mraise\u001b[0m \u001b[0mUndefinedVariableError\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mis_local\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0merr\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    217\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    218\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0mswapkey\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mold_key\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mstr\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnew_key\u001b[0m\u001b[1;33m:\u001b[0m \u001b[0mstr\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mnew_value\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mNone\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mUndefinedVariableError\u001b[0m: name 'EL_indefini_left' is not defined"]}], "source": ["missing_data_check..sort_values((('', 'durée')), ascending=False).head(15)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["cnt_full.query(\"StationId == 13 & ('2021-01-03 12:40:00' <= TimeStamp )\").to_clipboard()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Consecutive misassigned"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["cnt_full[\"ConsecutiveMis\"] = (\n", "    cnt_full.groupby(\"StationId\")\n", "    .apply(lambda df: df.EL_Misassigned.gt(0).diff().ne(0).cumsum())\n", "    .values\n", ")\n", "\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["def consecutive_misassigned(df):\n", "\n", "    df1 = pd.DataFrame()\n", "\n", "    df1.loc[0, \"debut\"] = df.TimeStamp.min()\n", "    df1.loc[0, \"fin\"] = df.TimeStamp.max()\n", "    debut = df.TimeStamp.min()\n", "    fin = df.TimeStamp.max()\n", "    df1.loc[0, \"durée\"] = fin - debut + pd.<PERSON><PERSON><PERSON>(minutes=10)\n", "\n", "    df1.loc[0, \"EL_Misassigned\"] = df.EL_Misassigned.sum()\n", "\n", "    df1.loc[0, \"wtc_AcWindSp_mean\"] = df.wtc_AcWindSp_mean.mean()\n", "    df1.loc[0, \"prev_AcWindSp\"] = df.prev_AcWindSp.mean()\n", "    df1.loc[0, \"next_AcWindSp\"] = df.next_AcWindSp.mean()\n", "\n", "    return df1\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th>debut</th>\n", "      <th>fin</th>\n", "      <th>du<PERSON>e</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "      <th>wtc_AcWindSp_mean</th>\n", "      <th>prev_AcWindSp</th>\n", "      <th>next_AcWindSp</th>\n", "    </tr>\n", "    <tr>\n", "      <th>StationId</th>\n", "      <th>ConsecutiveMis</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">1.0</th>\n", "      <th>2</th>\n", "      <th>0</th>\n", "      <td>2021-04-16 10:50:00</td>\n", "      <td>2021-04-16 12:20:00</td>\n", "      <td>0 days 01:40:00</td>\n", "      <td>2850.615811</td>\n", "      <td>0.450408</td>\n", "      <td>NaN</td>\n", "      <td>9.357675</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <th>0</th>\n", "      <td>2021-04-17 13:20:00</td>\n", "      <td>2021-04-17 13:30:00</td>\n", "      <td>0 days 00:20:00</td>\n", "      <td>373.141448</td>\n", "      <td>3.748524</td>\n", "      <td>NaN</td>\n", "      <td>11.039680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <th>0</th>\n", "      <td>2021-04-17 14:20:00</td>\n", "      <td>2021-04-17 14:30:00</td>\n", "      <td>0 days 00:20:00</td>\n", "      <td>215.087344</td>\n", "      <td>6.383126</td>\n", "      <td>NaN</td>\n", "      <td>11.263520</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <th>0</th>\n", "      <td>2021-04-21 14:00:00</td>\n", "      <td>2021-04-21 14:00:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>2.981697</td>\n", "      <td>7.630187</td>\n", "      <td>NaN</td>\n", "      <td>9.631365</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.0</th>\n", "      <th>2</th>\n", "      <th>0</th>\n", "      <td>2021-02-15 14:20:00</td>\n", "      <td>2021-02-15 14:20:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>5.430000</td>\n", "      <td>6.710000</td>\n", "      <td>12.230000</td>\n", "      <td>11.340000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">130.0</th>\n", "      <th>4</th>\n", "      <th>0</th>\n", "      <td>2021-04-14 11:10:00</td>\n", "      <td>2021-04-14 12:00:00</td>\n", "      <td>0 days 01:00:00</td>\n", "      <td>644.150000</td>\n", "      <td>0.978407</td>\n", "      <td>6.604868</td>\n", "      <td>6.412883</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <th>0</th>\n", "      <td>2021-04-23 10:10:00</td>\n", "      <td>2021-04-23 11:00:00</td>\n", "      <td>0 days 01:00:00</td>\n", "      <td>524.495341</td>\n", "      <td>6.826016</td>\n", "      <td>6.169237</td>\n", "      <td>6.477237</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <th>0</th>\n", "      <td>2021-04-23 11:30:00</td>\n", "      <td>2021-04-23 11:30:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>6.981000</td>\n", "      <td>4.881492</td>\n", "      <td>5.516262</td>\n", "      <td>6.020550</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <th>0</th>\n", "      <td>2021-06-30 09:30:00</td>\n", "      <td>2021-06-30 10:30:00</td>\n", "      <td>0 days 01:10:00</td>\n", "      <td>2091.973649</td>\n", "      <td>4.050038</td>\n", "      <td>11.506610</td>\n", "      <td>11.300563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <th>0</th>\n", "      <td>2021-08-24 09:30:00</td>\n", "      <td>2021-08-24 09:30:00</td>\n", "      <td>0 days 00:10:00</td>\n", "      <td>3.100000</td>\n", "      <td>4.890000</td>\n", "      <td>7.940000</td>\n", "      <td>7.270000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2452 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                         debut                 fin  \\\n", "StationId ConsecutiveMis                                             \n", "1.0       2              0 2021-04-16 10:50:00 2021-04-16 12:20:00   \n", "          4              0 2021-04-17 13:20:00 2021-04-17 13:30:00   \n", "          6              0 2021-04-17 14:20:00 2021-04-17 14:30:00   \n", "          8              0 2021-04-21 14:00:00 2021-04-21 14:00:00   \n", "2.0       2              0 2021-02-15 14:20:00 2021-02-15 14:20:00   \n", "...                                        ...                 ...   \n", "130.0     4              0 2021-04-14 11:10:00 2021-04-14 12:00:00   \n", "          6              0 2021-04-23 10:10:00 2021-04-23 11:00:00   \n", "          8              0 2021-04-23 11:30:00 2021-04-23 11:30:00   \n", "          10             0 2021-06-30 09:30:00 2021-06-30 10:30:00   \n", "          12             0 2021-08-24 09:30:00 2021-08-24 09:30:00   \n", "\n", "                                     du<PERSON><PERSON>sassigned  wtc_AcWindSp_mean  \\\n", "StationId ConsecutiveMis                                                        \n", "1.0       2              0 0 days 01:40:00     2850.615811           0.450408   \n", "          4              0 0 days 00:20:00      373.141448           3.748524   \n", "          6              0 0 days 00:20:00      215.087344           6.383126   \n", "          8              0 0 days 00:10:00        2.981697           7.630187   \n", "2.0       2              0 0 days 00:10:00        5.430000           6.710000   \n", "...                                    ...             ...                ...   \n", "130.0     4              0 0 days 01:00:00      644.150000           0.978407   \n", "          6              0 0 days 01:00:00      524.495341           6.826016   \n", "          8              0 0 days 00:10:00        6.981000           4.881492   \n", "          10             0 0 days 01:10:00     2091.973649           4.050038   \n", "          12             0 0 days 00:10:00        3.100000           4.890000   \n", "\n", "                            prev_AcWindSp  next_AcWindSp  \n", "StationId ConsecutiveMis                                  \n", "1.0       2              0            NaN       9.357675  \n", "          4              0            NaN      11.039680  \n", "          6              0            NaN      11.263520  \n", "          8              0            NaN       9.631365  \n", "2.0       2              0      12.230000      11.340000  \n", "...                                   ...            ...  \n", "130.0     4              0       6.604868       6.412883  \n", "          6              0       6.169237       6.477237  \n", "          8              0       5.516262       6.020550  \n", "          10             0      11.506610      11.300563  \n", "          12             0       7.940000       7.270000  \n", "\n", "[2452 rows x 7 columns]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["missasigned_check = (\n", "    cnt_full.query(\"EL_Misassigned > 0\")\n", "    .groupby([\"StationId\", \"ConsecutiveMis\"])\n", "    .apply(consecutive_misassigned)\n", ")\n", "\n", "missasigned_check\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["missasigned_check.sort_values(\"EL_Misassigned\", ascending=False).to_clipboard(\n", "    decimal=\",\"\n", ")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Comparaison Table from site before manual adjustment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TimeStamp</th>\n", "      <th>StationId</th>\n", "      <th>RealPeriod</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>Duration 20-25(s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>wtc_ActPower_max</th>\n", "      <th>wtc_AcWindSp_mean</th>\n", "      <th>prev_AcWindSp</th>\n", "      <th>next_AcWindSp</th>\n", "      <th>wtc_kWG1TotE_accum</th>\n", "      <th>Epot</th>\n", "      <th>wtc_ActPower_min</th>\n", "      <th>UK Text</th>\n", "      <th>Duration 115(s)</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-01-01 00:10:00</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2456.0</td>\n", "      <td>11.118440</td>\n", "      <td>NaN</td>\n", "      <td>10.166600</td>\n", "      <td>392.0</td>\n", "      <td>392.0</td>\n", "      <td>2178.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-01-01 00:20:00</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2450.0</td>\n", "      <td>10.807600</td>\n", "      <td>NaN</td>\n", "      <td>10.048530</td>\n", "      <td>382.0</td>\n", "      <td>382.0</td>\n", "      <td>1847.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-01-01 00:30:00</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2370.0</td>\n", "      <td>10.703420</td>\n", "      <td>NaN</td>\n", "      <td>10.105180</td>\n", "      <td>360.0</td>\n", "      <td>360.0</td>\n", "      <td>1732.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-01-01 00:40:00</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2368.0</td>\n", "      <td>10.201080</td>\n", "      <td>NaN</td>\n", "      <td>10.201020</td>\n", "      <td>328.0</td>\n", "      <td>328.0</td>\n", "      <td>1637.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-01-01 00:50:00</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2401.0</td>\n", "      <td>9.827996</td>\n", "      <td>NaN</td>\n", "      <td>9.403813</td>\n", "      <td>297.0</td>\n", "      <td>297.0</td>\n", "      <td>1196.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6301095</th>\n", "      <td>2021-11-30 23:30:00</td>\n", "      <td>131</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>589.0</td>\n", "      <td>5.290000</td>\n", "      <td>5.53</td>\n", "      <td>NaN</td>\n", "      <td>63.0</td>\n", "      <td>63.0</td>\n", "      <td>228.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6301096</th>\n", "      <td>2021-11-30 23:40:00</td>\n", "      <td>131</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>497.0</td>\n", "      <td>5.260000</td>\n", "      <td>5.36</td>\n", "      <td>NaN</td>\n", "      <td>59.0</td>\n", "      <td>59.0</td>\n", "      <td>234.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6301097</th>\n", "      <td>2021-11-30 23:50:00</td>\n", "      <td>131</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>573.0</td>\n", "      <td>5.420000</td>\n", "      <td>5.78</td>\n", "      <td>NaN</td>\n", "      <td>64.0</td>\n", "      <td>64.0</td>\n", "      <td>260.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6301098</th>\n", "      <td>2021-12-01 00:00:00</td>\n", "      <td>131</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>486.0</td>\n", "      <td>5.230000</td>\n", "      <td>5.59</td>\n", "      <td>NaN</td>\n", "      <td>61.0</td>\n", "      <td>61.0</td>\n", "      <td>282.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6301099</th>\n", "      <td>2021-12-01 00:10:00</td>\n", "      <td>131</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6301100 rows × 17 columns</p>\n", "</div>"], "text/plain": ["                  TimeStamp  StationId  RealPeriod  ELX  ELNX  \\\n", "0       2021-01-01 00:10:00          1         0.0  0.0   0.0   \n", "1       2021-01-01 00:20:00          1         0.0  0.0   0.0   \n", "2       2021-01-01 00:30:00          1         0.0  0.0   0.0   \n", "3       2021-01-01 00:40:00          1         0.0  0.0   0.0   \n", "4       2021-01-01 00:50:00          1         0.0  0.0   0.0   \n", "...                     ...        ...         ...  ...   ...   \n", "6301095 2021-11-30 23:30:00        131         0.0  0.0   0.0   \n", "6301096 2021-11-30 23:40:00        131         0.0  0.0   0.0   \n", "6301097 2021-11-30 23:50:00        131         0.0  0.0   0.0   \n", "6301098 2021-12-01 00:00:00        131         0.0  0.0   0.0   \n", "6301099 2021-12-01 00:10:00        131         0.0  0.0   0.0   \n", "\n", "         Duration 20-25(s)  EL_indefini_left  wtc_ActPower_max  \\\n", "0                      0.0               0.0            2456.0   \n", "1                      0.0               0.0            2450.0   \n", "2                      0.0               0.0            2370.0   \n", "3                      0.0               0.0            2368.0   \n", "4                      0.0               0.0            2401.0   \n", "...                    ...               ...               ...   \n", "6301095                0.0               0.0             589.0   \n", "6301096                0.0               0.0             497.0   \n", "6301097                0.0               0.0             573.0   \n", "6301098                0.0               0.0             486.0   \n", "6301099                0.0               0.0               0.0   \n", "\n", "         wtc_AcWindSp_mean  prev_AcWindSp  next_AcWindSp  wtc_kWG1TotE_accum  \\\n", "0                11.118440            NaN      10.166600               392.0   \n", "1                10.807600            NaN      10.048530               382.0   \n", "2                10.703420            NaN      10.105180               360.0   \n", "3                10.201080            NaN      10.201020               328.0   \n", "4                 9.827996            NaN       9.403813               297.0   \n", "...                    ...            ...            ...                 ...   \n", "6301095           5.290000           5.53            NaN                63.0   \n", "6301096           5.260000           5.36            NaN                59.0   \n", "6301097           5.420000           5.78            NaN                64.0   \n", "6301098           5.230000           5.59            NaN                61.0   \n", "6301099           0.000000           0.00            NaN                 0.0   \n", "\n", "          Epot  wtc_ActPower_min UK Text  Duration 115(s)  EL_Misassigned  \n", "0        392.0            2178.0       0              0.0             0.0  \n", "1        382.0            1847.0       0              0.0             0.0  \n", "2        360.0            1732.0       0              0.0             0.0  \n", "3        328.0            1637.0       0              0.0             0.0  \n", "4        297.0            1196.0       0              0.0             0.0  \n", "...        ...               ...     ...              ...             ...  \n", "6301095   63.0             228.0       0              0.0             0.0  \n", "6301096   59.0             234.0       0              0.0             0.0  \n", "6301097   64.0             260.0       0              0.0             0.0  \n", "6301098   61.0             282.0       0              0.0             0.0  \n", "6301099    0.0               0.0       0              0.0             0.0  \n", "\n", "[6301100 rows x 17 columns]"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["cumul_results\n"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["cumul_results_grp = (\n", "    cumul_results[\n", "        [\n", "            \"TimeStamp\",\n", "            \"wtc_kWG1TotE_accum\",\n", "            \"ELX\",\n", "            \"ELNX\",\n", "            \"EL_2006\",\n", "            \"EL_PowerRed\",\n", "            \"EL_Misassigned\",\n", "            \"EL_indefini_left\",\n", "        ]\n", "    ]\n", "    .groupby(cumul_results.TimeStamp.dt.month)\n", "    .sum()\n", ")\n", "\n", "Ep_cumul_grp = cumul_results_grp[\"wtc_kWG1TotE_accum\"]\n", "ELX_cumul_grp = cumul_results_grp[\"ELX\"]\n", "ELNX_cumul_grp = cumul_results_grp[\"ELNX\"]\n", "EL_2006_cumul_grp = cumul_results_grp[\"EL_2006\"]\n", "EL_PowerRed_cumul_grp = cumul_results_grp[\"EL_PowerRed\"]\n", "EL_Misassigned_cumul_grp = cumul_results_grp[\"EL_Misassigned\"]\n"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>wtc_kWG1TotE_accum</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>EL_2006</th>\n", "      <th>EL_PowerRed</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>Epot_eq</th>\n", "      <th>YBA(%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>TimeStamp</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>89447234.0</td>\n", "      <td>176540.453482</td>\n", "      <td>2.156876e+06</td>\n", "      <td>0.00</td>\n", "      <td>43542.94</td>\n", "      <td>15974.227697</td>\n", "      <td>5894.67</td>\n", "      <td>9.188371e+07</td>\n", "      <td>0.9752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>65658295.0</td>\n", "      <td>142502.130000</td>\n", "      <td>4.253592e+06</td>\n", "      <td>59866.71</td>\n", "      <td>67115.63</td>\n", "      <td>16232.780000</td>\n", "      <td>43205.36</td>\n", "      <td>7.032459e+07</td>\n", "      <td>0.9354</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>92715980.0</td>\n", "      <td>267777.137194</td>\n", "      <td>4.339316e+06</td>\n", "      <td>212226.53</td>\n", "      <td>64641.13</td>\n", "      <td>50982.268303</td>\n", "      <td>67346.19</td>\n", "      <td>9.792779e+07</td>\n", "      <td>0.9490</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>76348850.0</td>\n", "      <td>684734.037690</td>\n", "      <td>2.227006e+06</td>\n", "      <td>2984.48</td>\n", "      <td>38235.57</td>\n", "      <td>408014.067855</td>\n", "      <td>15040.28</td>\n", "      <td>7.975104e+07</td>\n", "      <td>0.9608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>151713834.0</td>\n", "      <td>232297.379581</td>\n", "      <td>5.660947e+06</td>\n", "      <td>0.00</td>\n", "      <td>38499.81</td>\n", "      <td>73714.131660</td>\n", "      <td>68203.49</td>\n", "      <td>1.577578e+08</td>\n", "      <td>0.9627</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>143571671.0</td>\n", "      <td>282777.710130</td>\n", "      <td>2.987789e+06</td>\n", "      <td>0.00</td>\n", "      <td>40051.71</td>\n", "      <td>112997.032970</td>\n", "      <td>486835.25</td>\n", "      <td>1.470353e+08</td>\n", "      <td>0.9776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>176693383.0</td>\n", "      <td>202248.847570</td>\n", "      <td>1.977595e+06</td>\n", "      <td>0.00</td>\n", "      <td>33435.12</td>\n", "      <td>60719.789047</td>\n", "      <td>92607.29</td>\n", "      <td>1.790008e+08</td>\n", "      <td>0.9879</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>141525830.0</td>\n", "      <td>302118.690000</td>\n", "      <td>2.167141e+06</td>\n", "      <td>0.00</td>\n", "      <td>25500.51</td>\n", "      <td>25639.180000</td>\n", "      <td>445180.94</td>\n", "      <td>1.440717e+08</td>\n", "      <td>0.9842</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>97217492.0</td>\n", "      <td>340517.990000</td>\n", "      <td>2.247897e+06</td>\n", "      <td>0.00</td>\n", "      <td>21913.94</td>\n", "      <td>44506.290000</td>\n", "      <td>21615.54</td>\n", "      <td>9.989424e+07</td>\n", "      <td>0.9762</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>76354844.0</td>\n", "      <td>177385.430000</td>\n", "      <td>1.601698e+06</td>\n", "      <td>0.00</td>\n", "      <td>4743.86</td>\n", "      <td>30947.040000</td>\n", "      <td>20869.00</td>\n", "      <td>7.817436e+07</td>\n", "      <td>0.9786</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>61774698.0</td>\n", "      <td>336097.450000</td>\n", "      <td>1.627837e+06</td>\n", "      <td>0.00</td>\n", "      <td>4728.12</td>\n", "      <td>95820.670000</td>\n", "      <td>19259.23</td>\n", "      <td>6.384391e+07</td>\n", "      <td>0.9714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>7975.0</td>\n", "      <td>0.000000</td>\n", "      <td>7.372000e+01</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.000000</td>\n", "      <td>0.00</td>\n", "      <td>8.048720e+03</td>\n", "      <td>0.9908</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           wtc_kWG1TotE_accum            ELX          ELNX    EL_2006  \\\n", "TimeStamp                                                               \n", "1                  89447234.0  176540.453482  2.156876e+06       0.00   \n", "2                  65658295.0  142502.130000  4.253592e+06   59866.71   \n", "3                  92715980.0  267777.137194  4.339316e+06  212226.53   \n", "4                  76348850.0  684734.037690  2.227006e+06    2984.48   \n", "5                 151713834.0  232297.379581  5.660947e+06       0.00   \n", "6                 143571671.0  282777.710130  2.987789e+06       0.00   \n", "7                 176693383.0  202248.847570  1.977595e+06       0.00   \n", "8                 141525830.0  302118.690000  2.167141e+06       0.00   \n", "9                  97217492.0  340517.990000  2.247897e+06       0.00   \n", "10                 76354844.0  177385.430000  1.601698e+06       0.00   \n", "11                 61774698.0  336097.450000  1.627837e+06       0.00   \n", "12                     7975.0       0.000000  7.372000e+01       0.00   \n", "\n", "           EL_PowerRed  EL_Misassigned  EL_indefini_left       Epot_eq  YBA(%)  \n", "TimeStamp                                                                       \n", "1             43542.94    15974.227697           5894.67  9.188371e+07  0.9752  \n", "2             67115.63    16232.780000          43205.36  7.032459e+07  0.9354  \n", "3             64641.13    50982.268303          67346.19  9.792779e+07  0.9490  \n", "4             38235.57   408014.067855          15040.28  7.975104e+07  0.9608  \n", "5             38499.81    73714.131660          68203.49  1.577578e+08  0.9627  \n", "6             40051.71   112997.032970         486835.25  1.470353e+08  0.9776  \n", "7             33435.12    60719.789047          92607.29  1.790008e+08  0.9879  \n", "8             25500.51    25639.180000         445180.94  1.440717e+08  0.9842  \n", "9             21913.94    44506.290000          21615.54  9.989424e+07  0.9762  \n", "10             4743.86    30947.040000          20869.00  7.817436e+07  0.9786  \n", "11             4728.12    95820.670000          19259.23  6.384391e+07  0.9714  \n", "12                0.00        0.000000              0.00  8.048720e+03  0.9908  "]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["ELNX_eq_cumul_grp = (\n", "    ELNX_cumul_grp\n", "    + EL_2006_cumul_grp\n", "    + EL_PowerRed_cumul_grp\n", "    + EL_Misassigned_cumul_grp\n", ")\n", "ELX_eq_cumul_grp = ELX_cumul_grp - EL_Misassigned_cumul_grp\n", "Epot_eq_cumul_grp = (\n", "    Ep_cumul_grp\n", "    + ELX_cumul_grp\n", "    + ELNX_eq_cumul_grp\n", "    + EL_2006_cumul_grp\n", "    + EL_PowerRed_cumul_grp\n", ")\n", "\n", "cumul_results_grp[\"Epot_eq\"] = Epot_eq_cumul_grp\n", "\n", "cumul_results_grp[\"YBA(%)\"] = round(\n", "    (Ep_cumul_grp + ELX_eq_cumul_grp) / (Epot_eq_cumul_grp), 4\n", ")\n", "\n", "cumul_results_grp.to_clipboard(decimal=\",\")\n", "cumul_results_grp\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cumul_results.query(\"TimeStamp.dt.month == 3 & StationId == 75\").to_clipboard(\n", "    decimal=\",\"\n", ")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Manual adjustments"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> 75 => 2021-03"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Fault: Manual stop reset but not concurrent alarms\n", "\n", "mask = (cumul_results.StationId == 75) & (cumul_results.TimeStamp.dt.month == 3)\n", "\n", "cumul_results.loc[mask, \"ELNX\"] = (\n", "    cumul_results.loc[mask, \"ELNX\"] + cumul_results.loc[mask, \"ELX\"]\n", ")\n", "          \n", "cumul_results.loc[mask, \"ELX\"] = 0\n", "cumul_results.loc[mask, \"EL_Misassigned\"] = 0\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Comparaison Table from site after manual adjustment"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>wtc_kWG1TotE_accum</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>EL_2006</th>\n", "      <th>EL_PowerRed</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th>ELX_eq</th>\n", "      <th>ELNX_eq</th>\n", "      <th>Epot_eq</th>\n", "      <th>YBA(%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>TimeStamp</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>44163679.0</td>\n", "      <td>268654.69</td>\n", "      <td>2202536.58</td>\n", "      <td>0.0</td>\n", "      <td>3641.48</td>\n", "      <td>6324.11</td>\n", "      <td>7421.66</td>\n", "      <td>262330.58</td>\n", "      <td>2212502.17</td>\n", "      <td>46638511.75</td>\n", "      <td>0.9526</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           wtc_kWG1TotE_accum        ELX        ELNX  EL_2006  EL_PowerRed  \\\n", "TimeStamp                                                                    \n", "1                  44163679.0  268654.69  2202536.58      0.0      3641.48   \n", "\n", "           EL_Misassigned  EL_indefini_left     ELX_eq     ELNX_eq  \\\n", "TimeStamp                                                            \n", "1                 6324.11           7421.66  262330.58  2212502.17   \n", "\n", "               Epot_eq  YBA(%)  \n", "TimeStamp                       \n", "1          46638511.75  0.9526  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["cml_res_grp = (\n", "    cumul_results[\n", "        [\n", "            \"TimeStamp\",\n", "            \"wtc_kWG1TotE_accum\",\n", "            \"ELX\",\n", "            \"ELNX\",\n", "            \"EL_2006\",\n", "            \"EL_PowerRed\",\n", "            \"EL_Misassigned\",\n", "            \"EL_indefini_left\",\n", "        ]\n", "    ]\n", "    .groupby(cumul_results.TimeStamp.dt.month)\n", "    .sum()\n", ")\n", "\n", "cml_res_grp[\"ELX_eq\"] = cml_res_grp[\"ELX\"] - cml_res_grp[\"EL_Misassigned\"]\n", "\n", "cml_res_grp[\"ELNX_eq\"] = (\n", "    cml_res_grp[\"ELNX\"]\n", "    + cml_res_grp[\"EL_2006\"]\n", "    + cml_res_grp[\"EL_PowerRed\"]\n", "    + cml_res_grp[\"EL_Misassigned\"]\n", ")\n", "\n", "cml_res_grp[\"Epot_eq\"] = (\n", "    cml_res_grp[\"wtc_kWG1TotE_accum\"] + cml_res_grp[\"ELX_eq\"] + cml_res_grp[\"ELNX_eq\"]\n", ")\n", "\n", "cml_res_grp[\"YBA(%)\"] = round(\n", "    (cml_res_grp[\"wtc_kWG1TotE_accum\"] + cml_res_grp[\"ELX_eq\"])\n", "    / (cml_res_grp[\"Epot_eq\"]),\n", "    4,\n", ")\n", "\n", "cml_res_grp.to_clipboard(decimal=\",\")\n", "cml_res_grp\n"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['TimeStamp', 'StationId', 'RealPeriod', 'ELX', 'ELNX',\n", "       'Duration 20-25(s)', 'EL_indefini_left', 'wtc_ActPower_max',\n", "       'wtc_AcWindSp_mean', 'prev_AcWindSp', 'next_AcWindSp',\n", "       'wtc_kWG1TotE_accum', 'Epot', 'wtc_ActPower_min', 'UK Text',\n", "       'Duration 115(s)', 'EL_Misassigned', 'EL_2006', 'EL_PowerRed'],\n", "      dtype='object')"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Availability per month per turbine"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>TimeStamp</th>\n", "      <th colspan=\"5\" halign=\"left\">December-2021</th>\n", "      <th colspan=\"5\" halign=\"left\">January-2022</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>ELNX</th>\n", "      <th>ELX</th>\n", "      <th>EP</th>\n", "      <th>PEP</th>\n", "      <th>YBA(%)</th>\n", "      <th>ELNX</th>\n", "      <th>ELX</th>\n", "      <th>EP</th>\n", "      <th>PEP</th>\n", "      <th>YBA(%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>StationId</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>35192.27</td>\n", "      <td>2206.07</td>\n", "      <td>583258.0</td>\n", "      <td>620656.34</td>\n", "      <td>0.9433</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>21.0</td>\n", "      <td>21.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1972.30</td>\n", "      <td>1242.76</td>\n", "      <td>631550.0</td>\n", "      <td>634765.06</td>\n", "      <td>0.9969</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>15.0</td>\n", "      <td>15.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20032.37</td>\n", "      <td>1411.61</td>\n", "      <td>602253.0</td>\n", "      <td>623696.98</td>\n", "      <td>0.9679</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>16.0</td>\n", "      <td>16.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>695.84</td>\n", "      <td>1553.59</td>\n", "      <td>622555.0</td>\n", "      <td>624804.43</td>\n", "      <td>0.9989</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>17.0</td>\n", "      <td>17.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>16.00</td>\n", "      <td>1239.60</td>\n", "      <td>621318.0</td>\n", "      <td>622573.60</td>\n", "      <td>1.0000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>19.0</td>\n", "      <td>19.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>127</th>\n", "      <td>3486.15</td>\n", "      <td>1174.79</td>\n", "      <td>454186.0</td>\n", "      <td>458846.94</td>\n", "      <td>0.9924</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>16.0</td>\n", "      <td>16.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>28738.53</td>\n", "      <td>2606.34</td>\n", "      <td>429675.0</td>\n", "      <td>461019.87</td>\n", "      <td>0.9377</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>20.0</td>\n", "      <td>20.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>129</th>\n", "      <td>10804.96</td>\n", "      <td>1402.77</td>\n", "      <td>442078.0</td>\n", "      <td>454285.73</td>\n", "      <td>0.9762</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>25.0</td>\n", "      <td>25.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>3181.22</td>\n", "      <td>685.95</td>\n", "      <td>457755.0</td>\n", "      <td>461622.17</td>\n", "      <td>0.9931</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>26.0</td>\n", "      <td>26.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131</th>\n", "      <td>10811.04</td>\n", "      <td>914.76</td>\n", "      <td>456253.0</td>\n", "      <td>467978.80</td>\n", "      <td>0.9769</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>28.0</td>\n", "      <td>28.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>131 rows × 10 columns</p>\n", "</div>"], "text/plain": ["TimeStamp December-2021                                       January-2022  \\\n", "                   ELNX      ELX        EP        PEP  YBA(%)         ELNX   \n", "StationId                                                                    \n", "1              35192.27  2206.07  583258.0  620656.34  0.9433          0.0   \n", "2               1972.30  1242.76  631550.0  634765.06  0.9969          0.0   \n", "3              20032.37  1411.61  602253.0  623696.98  0.9679          0.0   \n", "4                695.84  1553.59  622555.0  624804.43  0.9989          0.0   \n", "5                 16.00  1239.60  621318.0  622573.60  1.0000          0.0   \n", "...                 ...      ...       ...        ...     ...          ...   \n", "127             3486.15  1174.79  454186.0  458846.94  0.9924          0.0   \n", "128            28738.53  2606.34  429675.0  461019.87  0.9377          0.0   \n", "129            10804.96  1402.77  442078.0  454285.73  0.9762          0.0   \n", "130             3181.22   685.95  457755.0  461622.17  0.9931          0.0   \n", "131            10811.04   914.76  456253.0  467978.80  0.9769          0.0   \n", "\n", "TimeStamp                          \n", "           ELX    EP   PEP YBA(%)  \n", "StationId                          \n", "1          0.0  21.0  21.0    1.0  \n", "2          0.0  15.0  15.0    1.0  \n", "3          0.0  16.0  16.0    1.0  \n", "4          0.0  17.0  17.0    1.0  \n", "5          0.0  19.0  19.0    1.0  \n", "...        ...   ...   ...    ...  \n", "127        0.0  16.0  16.0    1.0  \n", "128        0.0  20.0  20.0    1.0  \n", "129        0.0  25.0  25.0    1.0  \n", "130        0.0  26.0  26.0    1.0  \n", "131        0.0  28.0  28.0    1.0  \n", "\n", "[131 rows x 10 columns]"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["cml_res_grp_stid = (\n", "    cumul_results[\n", "        [\n", "            \"TimeStamp\",\n", "            \"StationId\",\n", "            \"wtc_kWG1TotE_accum\",\n", "            \"ELX\",\n", "            \"ELNX\",\n", "            \"EL_2006\",\n", "            \"EL_PowerRed\",\n", "            \"EL_Misassigned\",\n", "            \"EL_indefini_left\",\n", "        ]\n", "    ]\n", "    .groupby([cumul_results.TimeStamp.dt.strftime(\"%B-%Y\"), \"StationId\"])\n", "    .sum()\n", ")\n", "\n", "\n", "cml_res_grp_stid[\"ELX_eq\"] = (\n", "    cml_res_grp_stid[\"ELX\"] - cml_res_grp_stid[\"EL_Misassigned\"]\n", ")\n", "\n", "cml_res_grp_stid[\"ELNX_eq\"] = (\n", "    cml_res_grp_stid[\"ELNX\"]\n", "    + cml_res_grp_stid[\"EL_2006\"]\n", "    + cml_res_grp_stid[\"EL_PowerRed\"]\n", "    + cml_res_grp_stid[\"EL_Misassigned\"]\n", ")\n", "\n", "cml_res_grp_stid[\"Epot_eq\"] = (\n", "    cml_res_grp_stid[\"wtc_kWG1TotE_accum\"]\n", "    + cml_res_grp_stid[\"ELX_eq\"]\n", "    + cml_res_grp_stid[\"ELNX_eq\"]\n", ")\n", "\n", "cml_res_grp_stid[\"YBA(%)\"] = round(\n", "    (cml_res_grp_stid[\"wtc_kWG1TotE_accum\"] + cml_res_grp_stid[\"ELX_eq\"])\n", "    / (cml_res_grp_stid[\"Epot_eq\"]),\n", "    4,\n", ")\n", "\n", "cml_res_grp_stid = (\n", "    cml_res_grp_stid[[\"wtc_kWG1TotE_accum\", \"ELX_eq\", \"ELNX_eq\", \"Epot_eq\", \"YBA(%)\"]]\n", "    .unstack(\"TimeStamp\")\n", "    .swaplevel(axis=\"columns\")\n", ")\n", "cml_res_grp_stid.rename(\n", "    columns={\n", "        \"ELNX_eq\": \"ELNX\",\n", "        \"ELX_eq\": \"ELX\",\n", "        \"Epot_eq\": \"PEP\",\n", "        \"wtc_kWG1TotE_accum\": \"EP\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "cml_res_grp_stid.sort_index(axis=1, inplace=True)\n", "\n", "cml_res_grp_stid.to_clipboard(decimal=\",\")\n", "\n", "cml_res_grp_stid"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["cml_res_grp_stid.to_excel(\"Indispo_par_turbine_12-2021.xlsx\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Availability per month per turbine (Time)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>TimeStamp</th>\n", "      <th colspan=\"4\" halign=\"left\">December-2021</th>\n", "      <th colspan=\"4\" halign=\"left\">January-2022</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>Indispo. alarms temps SGRE (%)</th>\n", "      <th>Indispo. alarms temps SGRE (heures)</th>\n", "      <th>Indispo. alarms temps TAREC (%)</th>\n", "      <th>Indispo. alarms temps TAREC (heures)</th>\n", "      <th>Indispo. alarms temps SGRE (%)</th>\n", "      <th>Indispo. alarms temps SGRE (heures)</th>\n", "      <th>Indispo. alarms temps TAREC (%)</th>\n", "      <th>Indispo. alarms temps TAREC (heures)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>StationId</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.376941e-02</td>\n", "      <td>32.564444</td>\n", "      <td>0.014242</td>\n", "      <td>10.596111</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7.154271e-03</td>\n", "      <td>5.322778</td>\n", "      <td>0.011199</td>\n", "      <td>8.331944</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3.446834e-02</td>\n", "      <td>25.644444</td>\n", "      <td>0.019139</td>\n", "      <td>14.239167</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2.196087e-03</td>\n", "      <td>1.633889</td>\n", "      <td>0.015458</td>\n", "      <td>11.501111</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3.733572e-07</td>\n", "      <td>0.000278</td>\n", "      <td>0.016738</td>\n", "      <td>12.453333</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>127</th>\n", "      <td>5.312500e-03</td>\n", "      <td>3.952500</td>\n", "      <td>0.011915</td>\n", "      <td>8.864444</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>3.062201e-02</td>\n", "      <td>22.782778</td>\n", "      <td>0.019206</td>\n", "      <td>14.289167</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>129</th>\n", "      <td>1.485850e-02</td>\n", "      <td>11.054722</td>\n", "      <td>0.006133</td>\n", "      <td>4.562778</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>4.098716e-03</td>\n", "      <td>3.049444</td>\n", "      <td>0.002725</td>\n", "      <td>2.027500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131</th>\n", "      <td>1.561492e-02</td>\n", "      <td>11.617500</td>\n", "      <td>0.002725</td>\n", "      <td>2.027500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>131 rows × 8 columns</p>\n", "</div>"], "text/plain": ["TimeStamp                  December-2021                                      \\\n", "          Indispo. alarms temps SGRE (%) Indispo. alarms temps SGRE (heures)   \n", "StationId                                                                      \n", "1                           4.376941e-02                           32.564444   \n", "2                           7.154271e-03                            5.322778   \n", "3                           3.446834e-02                           25.644444   \n", "4                           2.196087e-03                            1.633889   \n", "5                           3.733572e-07                            0.000278   \n", "...                                  ...                                 ...   \n", "127                         5.312500e-03                            3.952500   \n", "128                         3.062201e-02                           22.782778   \n", "129                         1.485850e-02                           11.054722   \n", "130                         4.098716e-03                            3.049444   \n", "131                         1.561492e-02                           11.617500   \n", "\n", "TimeStamp                                  \\\n", "          Indispo. alarms temps TAREC (%)   \n", "StationId                                   \n", "1                                0.014242   \n", "2                                0.011199   \n", "3                                0.019139   \n", "4                                0.015458   \n", "5                                0.016738   \n", "...                                   ...   \n", "127                              0.011915   \n", "128                              0.019206   \n", "129                              0.006133   \n", "130                              0.002725   \n", "131                              0.002725   \n", "\n", "TimeStamp                                                        January-2022  \\\n", "          Indispo. alarms temps TAREC (heures) Indispo. alarms temps SGRE (%)   \n", "StationId                                                                       \n", "1                                    10.596111                            0.0   \n", "2                                     8.331944                            0.0   \n", "3                                    14.239167                            0.0   \n", "4                                    11.501111                            0.0   \n", "5                                    12.453333                            0.0   \n", "...                                        ...                            ...   \n", "127                                   8.864444                            0.0   \n", "128                                  14.289167                            0.0   \n", "129                                   4.562778                            0.0   \n", "130                                   2.027500                            0.0   \n", "131                                   2.027500                            0.0   \n", "\n", "TimeStamp                                                                      \\\n", "          Indispo. alarms temps SGRE (heures) Indispo. alarms temps TAREC (%)   \n", "StationId                                                                       \n", "1                                         0.0                             0.0   \n", "2                                         0.0                             0.0   \n", "3                                         0.0                             0.0   \n", "4                                         0.0                             0.0   \n", "5                                         0.0                             0.0   \n", "...                                       ...                             ...   \n", "127                                       0.0                             0.0   \n", "128                                       0.0                             0.0   \n", "129                                       0.0                             0.0   \n", "130                                       0.0                             0.0   \n", "131                                       0.0                             0.0   \n", "\n", "TimeStamp                                       \n", "          Indispo. alarms temps TAREC (heures)  \n", "StationId                                       \n", "1                                          0.0  \n", "2                                          0.0  \n", "3                                          0.0  \n", "4                                          0.0  \n", "5                                          0.0  \n", "...                                        ...  \n", "127                                        0.0  \n", "128                                        0.0  \n", "129                                        0.0  \n", "130                                        0.0  \n", "131                                        0.0  \n", "\n", "[131 rows x 8 columns]"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["heures_in_period = pd.Period(period_cumul).days_in_month * 24\n", "\n", "cumul_results[\"Period 1 eq(h)\"] = cumul_results[\"Period 1(s)\"] / 3600\n", "cumul_results[\"Period 0 eq(h)\"] = cumul_results[\"Period 0(s)\"] / 3600\n", "\n", "mask = cumul_results[\"EL_Misassigned\"] > 0\n", "\n", "cumul_results.loc[mask, \"Period 1 eq(h)\"] = (\n", "    cumul_results.loc[mask, \"Period 1 eq(h)\"]\n", "    + cumul_results.loc[mask, \"Period 0 eq(h)\"]\n", ")\n", "cumul_results.loc[(mask), \"Period 0 eq(h)\",] = 0\n", "\n", "\n", "cumul_results[\"Period 1 eq(%)\"] = cumul_results[\"Period 1 eq(h)\"] / heures_in_period\n", "cumul_results[\"Period 0 eq(%)\"] = cumul_results[\"Period 0 eq(h)\"] / heures_in_period\n", "\n", "\n", "cml_res_grp_stid = (\n", "    cumul_results[\n", "        [\n", "            \"TimeStamp\",\n", "            \"StationId\",\n", "            \"EL_2006\",\n", "            \"EL_PowerRed\",\n", "            \"EL_Misassigned\",\n", "            \"Period 0 eq(h)\",\n", "            \"Period 1 eq(h)\",\n", "            \"Period 0 eq(%)\",\n", "            \"Period 1 eq(%)\",\n", "        ]\n", "    ]\n", "    .groupby([cumul_results.TimeStamp.dt.strftime(\"%B-%Y\"), \"StationId\"])\n", "    .sum()\n", ")\n", "\n", "cml_res_grp_stid = (\n", "    cml_res_grp_stid[\n", "        [\"Period 0 eq(h)\", \"Period 1 eq(h)\", \"Period 0 eq(%)\", \"Period 1 eq(%)\",]\n", "    ]\n", "    .unstack(\"TimeStamp\")\n", "    .swaplevel(axis=\"columns\")\n", ")\n", "\n", "cml_res_grp_stid.rename(\n", "    columns={\n", "        \"Period 0 eq(h)\": \"Indispo. alarms temps TAREC (heures)\",\n", "        \"Period 1 eq(h)\": \"Indispo. alarms temps SGRE (heures)\",\n", "        \"Period 0 eq(%)\": \"Indispo. alarms temps TAREC (%)\",\n", "        \"Period 1 eq(%)\": \"Indispo. alarms temps SGRE (%)\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "cml_res_grp_stid.sort_index(axis=1, inplace=True)\n", "\n", "cml_res_grp_stid.to_clipboard(decimal=\",\")\n", "\n", "cml_res_grp_stid\n"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["cml_res_grp_stid.to_excel(\"Indispo_par_turbine_temps_12-2021.xlsx\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Availability per month per turbine (Energy & Time)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["30"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["(cumul_results.iloc[-1, 0] - cumul_results.iloc[0, 0]).round(\"1d\").days"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["Timestamp('2022-05-01 00:00:00')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["cumul_results.iloc[-1, 0]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["heures_in_period = (\n", "    (cumul_results.iloc[-1, 0] - cumul_results.iloc[0, 0]).round(\"1d\").days\n", ") * 24\n", "\n", "cumul_results[\"Period 1 eq(h)\"] = cumul_results[\"Period 1(s)\"] / 3600\n", "cumul_results[\"Period 0 eq(h)\"] = cumul_results[\"Period 0(s)\"] / 3600\n", "\n", "mask = cumul_results[\"EL_Misassigned\"] > 0\n", "\n", "cumul_results.loc[mask, \"Period 1 eq(h)\"] = (\n", "    cumul_results.loc[mask, \"Period 1 eq(h)\"]\n", "    + cumul_results.loc[mask, \"Period 0 eq(h)\"]\n", ")\n", "cumul_results.loc[(mask), \"Period 0 eq(h)\",] = 0\n", "\n", "\n", "cumul_results[\"Period 1 eq(%)\"] = cumul_results[\"Period 1 eq(h)\"] / heures_in_period\n", "cumul_results[\"Period 0 eq(%)\"] = cumul_results[\"Period 0 eq(h)\"] / heures_in_period\n", "\n", "\n", "cml_res_grp_stid_time = (\n", "    cumul_results[\n", "        [\n", "            \"TimeStamp\",\n", "            \"StationId\",\n", "            \"EL_2006\",\n", "            \"EL_PowerRed\",\n", "            \"EL_Misassigned\",\n", "            \"Period 0 eq(h)\",\n", "            \"Period 1 eq(h)\",\n", "            \"Period 0 eq(%)\",\n", "            \"Period 1 eq(%)\",\n", "        ]\n", "    ]\n", "    .groupby([cumul_results.TimeStamp.dt.strftime(\"%B-%Y\"), \"StationId\"])\n", "    .sum()\n", ")\n", "\n", "cml_res_grp_stid_time = (\n", "    cml_res_grp_stid_time[\n", "        [\"Period 0 eq(h)\", \"Period 1 eq(h)\", \"Period 0 eq(%)\", \"Period 1 eq(%)\",]\n", "    ]\n", "    .unstack(\"TimeStamp\")\n", "    .swaplevel(axis=\"columns\")\n", ")\n", "\n", "cml_res_grp_stid_time.rename(\n", "    columns={\n", "        \"Period 0 eq(h)\": \"Indispo. alarms temps TAREC (heures)\",\n", "        \"Period 1 eq(h)\": \"Indispo. alarms temps SGRE (heures)\",\n", "        \"Period 0 eq(%)\": \"Indispo. alarms temps TAREC (%)\",\n", "        \"Period 1 eq(%)\": \"Indispo. alarms temps SGRE (%)\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "cml_res_grp_stid_time.sort_index(axis=1, inplace=True)\n", "\n", "cml_res_grp_stid_time.to_clipboard(decimal=\",\")\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["cml_res_grp_stid_nrg = (\n", "    cumul_results[\n", "        [\n", "            \"TimeStamp\",\n", "            \"StationId\",\n", "            \"wtc_kWG1TotE_accum\",\n", "            \"ELX\",\n", "            \"ELNX\",\n", "            \"EL_2006\",\n", "            \"EL_PowerRed\",\n", "            \"EL_Misassigned\",\n", "            \"EL_indefini_left\",\n", "        ]\n", "    ]\n", "    .groupby([cumul_results.TimeStamp.dt.strftime(\"%B-%Y\"), \"StationId\"])\n", "    .sum()\n", ")\n", "\n", "\n", "cml_res_grp_stid_nrg[\"ELX_eq\"] = (\n", "    cml_res_grp_stid_nrg[\"ELX\"] - cml_res_grp_stid_nrg[\"EL_Misassigned\"]\n", ")\n", "\n", "cml_res_grp_stid_nrg[\"ELNX_eq\"] = (\n", "    cml_res_grp_stid_nrg[\"ELNX\"]\n", "    + cml_res_grp_stid_nrg[\"EL_2006\"]\n", "    + cml_res_grp_stid_nrg[\"EL_PowerRed\"]\n", "    + cml_res_grp_stid_nrg[\"EL_Misassigned\"]\n", ")\n", "\n", "cml_res_grp_stid_nrg[\"Epot_eq\"] = (\n", "    cml_res_grp_stid_nrg[\"wtc_kWG1TotE_accum\"]\n", "    + cml_res_grp_stid_nrg[\"ELX_eq\"]\n", "    + cml_res_grp_stid_nrg[\"ELNX_eq\"]\n", ")\n", "\n", "cml_res_grp_stid_nrg[\"YBA(%)\"] = round(\n", "    (cml_res_grp_stid_nrg[\"wtc_kWG1TotE_accum\"] + cml_res_grp_stid_nrg[\"ELX_eq\"])\n", "    / (cml_res_grp_stid_nrg[\"Epot_eq\"]),\n", "    4,\n", ")\n", "\n", "cml_res_grp_stid_nrg = (\n", "    cml_res_grp_stid_nrg[[\"wtc_kWG1TotE_accum\", \"ELX_eq\", \"ELNX_eq\", \"Epot_eq\", \"YBA(%)\"]]\n", "    .unstack(\"TimeStamp\")\n", "    .swaplevel(axis=\"columns\")\n", ")\n", "cml_res_grp_stid_nrg.rename(\n", "    columns={\n", "        \"ELNX_eq\": \"ELNX\",\n", "        \"ELX_eq\": \"ELX\",\n", "        \"Epot_eq\": \"PEP\",\n", "        \"wtc_kWG1TotE_accum\": \"EP\",\n", "    },\n", "    inplace=True,\n", ")\n", "\n", "cml_res_grp_stid_nrg.sort_index(axis=1, inplace=True)\n", "\n", "cml_res_grp_stid_nrg.to_clipboard(decimal=\",\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>TimeStamp</th>\n", "      <th colspan=\"5\" halign=\"left\">April-2022</th>\n", "      <th colspan=\"5\" halign=\"left\">May-2022</th>\n", "      <th colspan=\"4\" halign=\"left\">April-2022</th>\n", "      <th colspan=\"4\" halign=\"left\">May-2022</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>ELNX</th>\n", "      <th>ELX</th>\n", "      <th>EP</th>\n", "      <th>PEP</th>\n", "      <th>YBA(%)</th>\n", "      <th>ELNX</th>\n", "      <th>ELX</th>\n", "      <th>EP</th>\n", "      <th>PEP</th>\n", "      <th>YBA(%)</th>\n", "      <th>Indispo. alarms temps SGRE (%)</th>\n", "      <th>Indispo. alarms temps SGRE (heures)</th>\n", "      <th>Indispo. alarms temps TAREC (%)</th>\n", "      <th>Indispo. alarms temps TAREC (heures)</th>\n", "      <th>Indispo. alarms temps SGRE (%)</th>\n", "      <th>Indispo. alarms temps SGRE (heures)</th>\n", "      <th>Indispo. alarms temps TAREC (%)</th>\n", "      <th>Indispo. alarms temps TAREC (heures)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>StationId</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1.0</th>\n", "      <td>25121.66</td>\n", "      <td>224.40</td>\n", "      <td>994315.0</td>\n", "      <td>1019661.06</td>\n", "      <td>0.9754</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>254.0</td>\n", "      <td>1.0</td>\n", "      <td>0.034472</td>\n", "      <td>24.820000</td>\n", "      <td>0.001520</td>\n", "      <td>1.094444</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2.0</th>\n", "      <td>43554.70</td>\n", "      <td>209.70</td>\n", "      <td>949695.0</td>\n", "      <td>993459.40</td>\n", "      <td>0.9562</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>235.0</td>\n", "      <td>235.0</td>\n", "      <td>1.0</td>\n", "      <td>0.046940</td>\n", "      <td>33.796944</td>\n", "      <td>0.001092</td>\n", "      <td>0.786111</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3.0</th>\n", "      <td>25677.32</td>\n", "      <td>554.39</td>\n", "      <td>995528.0</td>\n", "      <td>1021759.71</td>\n", "      <td>0.9749</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>197.0</td>\n", "      <td>197.0</td>\n", "      <td>1.0</td>\n", "      <td>0.021884</td>\n", "      <td>15.756389</td>\n", "      <td>0.001012</td>\n", "      <td>0.728611</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4.0</th>\n", "      <td>21401.88</td>\n", "      <td>913.65</td>\n", "      <td>990279.0</td>\n", "      <td>1012594.53</td>\n", "      <td>0.9789</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>188.0</td>\n", "      <td>188.0</td>\n", "      <td>1.0</td>\n", "      <td>0.026834</td>\n", "      <td>19.320556</td>\n", "      <td>0.005948</td>\n", "      <td>4.282778</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5.0</th>\n", "      <td>32427.44</td>\n", "      <td>460.96</td>\n", "      <td>980964.0</td>\n", "      <td>1013852.40</td>\n", "      <td>0.9680</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>220.0</td>\n", "      <td>220.0</td>\n", "      <td>1.0</td>\n", "      <td>0.050396</td>\n", "      <td>36.285278</td>\n", "      <td>0.001042</td>\n", "      <td>0.750000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>127.0</th>\n", "      <td>18999.76</td>\n", "      <td>855.08</td>\n", "      <td>859149.0</td>\n", "      <td>879003.84</td>\n", "      <td>0.9784</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>169.0</td>\n", "      <td>169.0</td>\n", "      <td>1.0</td>\n", "      <td>0.030366</td>\n", "      <td>21.863333</td>\n", "      <td>0.001656</td>\n", "      <td>1.192500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128.0</th>\n", "      <td>136875.65</td>\n", "      <td>1488.58</td>\n", "      <td>690997.0</td>\n", "      <td>829361.23</td>\n", "      <td>0.8350</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>229.0</td>\n", "      <td>229.0</td>\n", "      <td>1.0</td>\n", "      <td>0.144383</td>\n", "      <td>103.955556</td>\n", "      <td>0.022996</td>\n", "      <td>16.556944</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>129.0</th>\n", "      <td>45872.13</td>\n", "      <td>1065.97</td>\n", "      <td>825014.0</td>\n", "      <td>871952.10</td>\n", "      <td>0.9474</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>211.0</td>\n", "      <td>211.0</td>\n", "      <td>1.0</td>\n", "      <td>0.042772</td>\n", "      <td>30.796111</td>\n", "      <td>0.002298</td>\n", "      <td>1.654722</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130.0</th>\n", "      <td>1770.26</td>\n", "      <td>1297.69</td>\n", "      <td>891575.0</td>\n", "      <td>894642.95</td>\n", "      <td>0.9980</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>198.0</td>\n", "      <td>198.0</td>\n", "      <td>1.0</td>\n", "      <td>0.002150</td>\n", "      <td>1.547778</td>\n", "      <td>0.001899</td>\n", "      <td>1.367500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131.0</th>\n", "      <td>8439.61</td>\n", "      <td>986.76</td>\n", "      <td>903398.0</td>\n", "      <td>912824.37</td>\n", "      <td>0.9908</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>168.0</td>\n", "      <td>168.0</td>\n", "      <td>1.0</td>\n", "      <td>0.010012</td>\n", "      <td>7.208333</td>\n", "      <td>0.001633</td>\n", "      <td>1.176111</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>131 rows × 18 columns</p>\n", "</div>"], "text/plain": ["TimeStamp April-2022                                        May-2022       \\\n", "                ELNX      ELX        EP         PEP  YBA(%)     ELNX  ELX   \n", "StationId                                                                   \n", "1.0         25121.66   224.40  994315.0  1019661.06  0.9754      0.0  0.0   \n", "2.0         43554.70   209.70  949695.0   993459.40  0.9562      0.0  0.0   \n", "3.0         25677.32   554.39  995528.0  1021759.71  0.9749      0.0  0.0   \n", "4.0         21401.88   913.65  990279.0  1012594.53  0.9789      0.0  0.0   \n", "5.0         32427.44   460.96  980964.0  1013852.40  0.9680      0.0  0.0   \n", "...              ...      ...       ...         ...     ...      ...  ...   \n", "127.0       18999.76   855.08  859149.0   879003.84  0.9784      0.0  0.0   \n", "128.0      136875.65  1488.58  690997.0   829361.23  0.8350      0.0  0.0   \n", "129.0       45872.13  1065.97  825014.0   871952.10  0.9474      0.0  0.0   \n", "130.0        1770.26  1297.69  891575.0   894642.95  0.9980      0.0  0.0   \n", "131.0        8439.61   986.76  903398.0   912824.37  0.9908      0.0  0.0   \n", "\n", "TimeStamp                                          April-2022  \\\n", "              EP    PEP YBA(%) Indispo. alarms temps SGRE (%)   \n", "StationId                                                       \n", "1.0        254.0  254.0    1.0                       0.034472   \n", "2.0        235.0  235.0    1.0                       0.046940   \n", "3.0        197.0  197.0    1.0                       0.021884   \n", "4.0        188.0  188.0    1.0                       0.026834   \n", "5.0        220.0  220.0    1.0                       0.050396   \n", "...          ...    ...    ...                            ...   \n", "127.0      169.0  169.0    1.0                       0.030366   \n", "128.0      229.0  229.0    1.0                       0.144383   \n", "129.0      211.0  211.0    1.0                       0.042772   \n", "130.0      198.0  198.0    1.0                       0.002150   \n", "131.0      168.0  168.0    1.0                       0.010012   \n", "\n", "TimeStamp                                                                      \\\n", "          Indispo. alarms temps SGRE (heures) Indispo. alarms temps TAREC (%)   \n", "StationId                                                                       \n", "1.0                                 24.820000                        0.001520   \n", "2.0                                 33.796944                        0.001092   \n", "3.0                                 15.756389                        0.001012   \n", "4.0                                 19.320556                        0.005948   \n", "5.0                                 36.285278                        0.001042   \n", "...                                       ...                             ...   \n", "127.0                               21.863333                        0.001656   \n", "128.0                              103.955556                        0.022996   \n", "129.0                               30.796111                        0.002298   \n", "130.0                                1.547778                        0.001899   \n", "131.0                                7.208333                        0.001633   \n", "\n", "TimeStamp                                                            May-2022  \\\n", "          Indispo. alarms temps TAREC (heures) Indispo. alarms temps SGRE (%)   \n", "StationId                                                                       \n", "1.0                                   1.094444                            0.0   \n", "2.0                                   0.786111                            0.0   \n", "3.0                                   0.728611                            0.0   \n", "4.0                                   4.282778                            0.0   \n", "5.0                                   0.750000                            0.0   \n", "...                                        ...                            ...   \n", "127.0                                 1.192500                            0.0   \n", "128.0                                16.556944                            0.0   \n", "129.0                                 1.654722                            0.0   \n", "130.0                                 1.367500                            0.0   \n", "131.0                                 1.176111                            0.0   \n", "\n", "TimeStamp                                                                      \\\n", "          Indispo. alarms temps SGRE (heures) Indispo. alarms temps TAREC (%)   \n", "StationId                                                                       \n", "1.0                                       0.0                             0.0   \n", "2.0                                       0.0                             0.0   \n", "3.0                                       0.0                             0.0   \n", "4.0                                       0.0                             0.0   \n", "5.0                                       0.0                             0.0   \n", "...                                       ...                             ...   \n", "127.0                                     0.0                             0.0   \n", "128.0                                     0.0                             0.0   \n", "129.0                                     0.0                             0.0   \n", "130.0                                     0.0                             0.0   \n", "131.0                                     0.0                             0.0   \n", "\n", "TimeStamp                                       \n", "          Indispo. alarms temps TAREC (heures)  \n", "StationId                                       \n", "1.0                                        0.0  \n", "2.0                                        0.0  \n", "3.0                                        0.0  \n", "4.0                                        0.0  \n", "5.0                                        0.0  \n", "...                                        ...  \n", "127.0                                      0.0  \n", "128.0                                      0.0  \n", "129.0                                      0.0  \n", "130.0                                      0.0  \n", "131.0                                      0.0  \n", "\n", "[131 rows x 18 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["cml_res_grp_stid_nrg_time = pd.concat([cml_res_grp_stid_nrg, cml_res_grp_stid_time], axis=1)\n", "cml_res_grp_stid_nrg_time"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["cml_res_grp_stid_nrg_time.to_excel(\"Indispo_par_turbine/Indispo_par_turbine_04-2022.xlsx\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Comparison as of 09/2024"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["-4256.0"]}, "metadata": {}, "output_type": "display_data"}], "source": ["(results.query(\"wtc_ActPower_min > 0\").wtc_kWG1TotE_accum.sum()-\n", " (results.query(\"wtc_ActPower_min > 0\").wtc_ActPower_mean/6).sum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["323.71"]}, "metadata": {}, "output_type": "display_data"}], "source": ["round(1942.23999/6, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["(31877.0, 31861.404)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["(results.query(\"TimeStamp == '2024-02-29 00:40:00' & RealPeriod == 0\").wtc_kWG1TotE_accum.sum(),\n", "(results.query(\"TimeStamp == '2024-02-29 00:40:00' & RealPeriod == 0\").wtc_ActPower_mean/6).sum())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def CF(M, WTN=131, AL_ALL=0.08):\n", "    def AL(M):\n", "        return AL_ALL * (M - 1) / (WTN - 1)\n", "\n", "    return (1 - AL_ALL) / (1 - AL(M))\n", "\n", "\n", "def ep_cf(x):\n", "    M = len(x)\n", "    x = x.mean()\n", "    x = round(x * CF(M), 2)\n", "    return x\n", "\n", "\n", "def cf_column(x):\n", "    M = len(x)\n", "    return CF(M)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>StationId</th>\n", "      <th>TimeStamp</th>\n", "      <th>RealPeriod</th>\n", "      <th>Period 0(s)</th>\n", "      <th>Period 1(s)</th>\n", "      <th>UK Text</th>\n", "      <th>Duration 2006(s)</th>\n", "      <th>wtc_kWG1Tot_accum</th>\n", "      <th>wtc_kWG1TotE_accum</th>\n", "      <th>wtc_kWG1TotI_accum</th>\n", "      <th>wtc_ActPower_min</th>\n", "      <th>wtc_ActPower_max</th>\n", "      <th>wtc_ActPower_mean</th>\n", "      <th>wtc_AcWindSp_mean</th>\n", "      <th>wtc_ActualWindDirection_mean</th>\n", "      <th>met_WindSpeedRot_mean_38</th>\n", "      <th>met_WindSpeedRot_mean_39</th>\n", "      <th>met_WindSpeedRot_mean_246</th>\n", "      <th>met_WinddirectionRot_mean_38</th>\n", "      <th>met_WinddirectionRot_mean_39</th>\n", "      <th>met_WinddirectionRot_mean_246</th>\n", "      <th>wtc_PowerRed_timeon</th>\n", "      <th>Epot</th>\n", "      <th>Correction Factor</th>\n", "      <th>Available Turbines</th>\n", "      <th>EL</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>EL_indefini</th>\n", "      <th>prev_AcWindSp</th>\n", "      <th>next_AcWindSp</th>\n", "      <th>prev_ActPower_min</th>\n", "      <th>next_ActPower_min</th>\n", "      <th>prev_Alarme</th>\n", "      <th>next_<PERSON><PERSON>e</th>\n", "      <th>DiffV1</th>\n", "      <th>DiffV2</th>\n", "      <th>EL_PowerRed</th>\n", "      <th>EL_2006</th>\n", "      <th>EL_wind</th>\n", "      <th>Duration lowind(s)</th>\n", "      <th>EL_wind_start</th>\n", "      <th>Duration lowind_start(s)</th>\n", "      <th>EL_alarm_start</th>\n", "      <th>Duration alarm_start(s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>100086</th>\n", "      <td>24</td>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>600.00</td>\n", "      <td>0.00</td>\n", "      <td>600.00</td>\n", "      <td>Timeout  DC-circuit charging</td>\n", "      <td>0.00</td>\n", "      <td>-2.00</td>\n", "      <td>0.00</td>\n", "      <td>2.00</td>\n", "      <td>-26.00</td>\n", "      <td>-7.00</td>\n", "      <td>-7.76</td>\n", "      <td>8.08</td>\n", "      <td>32.90</td>\n", "      <td>10.55</td>\n", "      <td>10.54</td>\n", "      <td>10.63</td>\n", "      <td>37.54</td>\n", "      <td>35.85</td>\n", "      <td>33.04</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>1.00</td>\n", "      <td>124.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>9.45</td>\n", "      <td>10.23</td>\n", "      <td>1475.00</td>\n", "      <td>1283.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.38</td>\n", "      <td>2.15</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200310</th>\n", "      <td>48</td>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>600.00</td>\n", "      <td>0.00</td>\n", "      <td>600.00</td>\n", "      <td>DC fuse blown</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>10.55</td>\n", "      <td>10.54</td>\n", "      <td>10.63</td>\n", "      <td>37.54</td>\n", "      <td>35.85</td>\n", "      <td>33.04</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>1.00</td>\n", "      <td>124.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>10.15</td>\n", "      <td>7.95</td>\n", "      <td>1898.00</td>\n", "      <td>844.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>10.15</td>\n", "      <td>7.95</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>262950</th>\n", "      <td>63</td>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>600.00</td>\n", "      <td>0.00</td>\n", "      <td>600.00</td>\n", "      <td>Manual stop</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>-5.00</td>\n", "      <td>-3.00</td>\n", "      <td>-3.92</td>\n", "      <td>8.22</td>\n", "      <td>49.08</td>\n", "      <td>10.55</td>\n", "      <td>10.54</td>\n", "      <td>10.63</td>\n", "      <td>37.54</td>\n", "      <td>35.85</td>\n", "      <td>33.04</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>1.00</td>\n", "      <td>124.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>9.46</td>\n", "      <td>7.62</td>\n", "      <td>1149.00</td>\n", "      <td>706.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.24</td>\n", "      <td>-0.60</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>279654</th>\n", "      <td>67</td>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>600.00</td>\n", "      <td>0.00</td>\n", "      <td>600.00</td>\n", "      <td>Converter trip, external</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>10.55</td>\n", "      <td>10.54</td>\n", "      <td>10.63</td>\n", "      <td>37.54</td>\n", "      <td>35.85</td>\n", "      <td>33.04</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>1.00</td>\n", "      <td>124.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>8.06</td>\n", "      <td>9.23</td>\n", "      <td>773.00</td>\n", "      <td>1035.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>8.06</td>\n", "      <td>9.23</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>429990</th>\n", "      <td>103</td>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>149.00</td>\n", "      <td>150.00</td>\n", "      <td>0.00</td>\n", "      <td>-30.00</td>\n", "      <td>1845.00</td>\n", "      <td>897.92</td>\n", "      <td>7.05</td>\n", "      <td>21.01</td>\n", "      <td>10.55</td>\n", "      <td>10.54</td>\n", "      <td>10.63</td>\n", "      <td>37.54</td>\n", "      <td>35.85</td>\n", "      <td>33.04</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>1.00</td>\n", "      <td>124.00</td>\n", "      <td>151.68</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>151.68</td>\n", "      <td>9.81</td>\n", "      <td>7.78</td>\n", "      <td>1453.00</td>\n", "      <td>715.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>2.76</td>\n", "      <td>0.72</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>151.68</td>\n", "      <td>600.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>438342</th>\n", "      <td>105</td>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>600.00</td>\n", "      <td>0.00</td>\n", "      <td>600.00</td>\n", "      <td>Smoke in the A3 box</td>\n", "      <td>0.00</td>\n", "      <td>-1.00</td>\n", "      <td>0.00</td>\n", "      <td>1.00</td>\n", "      <td>-2.00</td>\n", "      <td>-1.00</td>\n", "      <td>-1.78</td>\n", "      <td>6.20</td>\n", "      <td>36.07</td>\n", "      <td>10.55</td>\n", "      <td>10.54</td>\n", "      <td>10.63</td>\n", "      <td>37.54</td>\n", "      <td>35.85</td>\n", "      <td>33.04</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>1.00</td>\n", "      <td>124.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>7.78</td>\n", "      <td>8.29</td>\n", "      <td>715.00</td>\n", "      <td>866.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.58</td>\n", "      <td>2.10</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>534390</th>\n", "      <td>128</td>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>600.00</td>\n", "      <td>0.00</td>\n", "      <td>600.00</td>\n", "      <td>Gear oil temperature high</td>\n", "      <td>0.00</td>\n", "      <td>-3.00</td>\n", "      <td>0.00</td>\n", "      <td>3.00</td>\n", "      <td>-30.00</td>\n", "      <td>-11.00</td>\n", "      <td>-15.05</td>\n", "      <td>6.15</td>\n", "      <td>39.12</td>\n", "      <td>10.55</td>\n", "      <td>10.54</td>\n", "      <td>10.63</td>\n", "      <td>37.54</td>\n", "      <td>35.85</td>\n", "      <td>33.04</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>1.00</td>\n", "      <td>124.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>301.68</td>\n", "      <td>0.00</td>\n", "      <td>8.88</td>\n", "      <td>8.31</td>\n", "      <td>856.00</td>\n", "      <td>786.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>2.72</td>\n", "      <td>2.15</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        StationId           TimeStamp  RealPeriod  Period 0(s)  Period 1(s)  \\\n", "100086         24 2024-02-29 01:10:00      600.00         0.00       600.00   \n", "200310         48 2024-02-29 01:10:00      600.00         0.00       600.00   \n", "262950         63 2024-02-29 01:10:00      600.00         0.00       600.00   \n", "279654         67 2024-02-29 01:10:00      600.00         0.00       600.00   \n", "429990        103 2024-02-29 01:10:00        0.00         0.00         0.00   \n", "438342        105 2024-02-29 01:10:00      600.00         0.00       600.00   \n", "534390        128 2024-02-29 01:10:00      600.00         0.00       600.00   \n", "\n", "                             UK Text  Duration 2006(s)  wtc_kWG1Tot_accum  \\\n", "100086  Timeout  DC-circuit charging              0.00              -2.00   \n", "200310                 DC fuse blown              0.00               0.00   \n", "262950                   Manual stop              0.00               0.00   \n", "279654      Converter trip, external              0.00               0.00   \n", "429990                             0              0.00             149.00   \n", "438342           Smoke in the A3 box              0.00              -1.00   \n", "534390     Gear oil temperature high              0.00              -3.00   \n", "\n", "        wtc_kWG1TotE_accum  wtc_kWG1TotI_accum  wtc_ActPower_min  \\\n", "100086                0.00                2.00            -26.00   \n", "200310                0.00                0.00              0.00   \n", "262950                0.00                0.00             -5.00   \n", "279654                0.00                0.00              0.00   \n", "429990              150.00                0.00            -30.00   \n", "438342                0.00                1.00             -2.00   \n", "534390                0.00                3.00            -30.00   \n", "\n", "        wtc_ActPower_max  wtc_ActPower_mean  wtc_AcWindSp_mean  \\\n", "100086             -7.00              -7.76               8.08   \n", "200310              0.00               0.00               0.00   \n", "262950             -3.00              -3.92               8.22   \n", "279654              0.00               0.00               0.00   \n", "429990           1845.00             897.92               7.05   \n", "438342             -1.00              -1.78               6.20   \n", "534390            -11.00             -15.05               6.15   \n", "\n", "        wtc_ActualWindDirection_mean  met_WindSpeedRot_mean_38  \\\n", "100086                         32.90                     10.55   \n", "200310                          0.00                     10.55   \n", "262950                         49.08                     10.55   \n", "279654                          0.00                     10.55   \n", "429990                         21.01                     10.55   \n", "438342                         36.07                     10.55   \n", "534390                         39.12                     10.55   \n", "\n", "        met_WindSpeedRot_mean_39  met_WindSpeedRot_mean_246  \\\n", "100086                     10.54                      10.63   \n", "200310                     10.54                      10.63   \n", "262950                     10.54                      10.63   \n", "279654                     10.54                      10.63   \n", "429990                     10.54                      10.63   \n", "438342                     10.54                      10.63   \n", "534390                     10.54                      10.63   \n", "\n", "        met_WinddirectionRot_mean_38  met_WinddirectionRot_mean_39  \\\n", "100086                         37.54                         35.85   \n", "200310                         37.54                         35.85   \n", "262950                         37.54                         35.85   \n", "279654                         37.54                         35.85   \n", "429990                         37.54                         35.85   \n", "438342                         37.54                         35.85   \n", "534390                         37.54                         35.85   \n", "\n", "        met_WinddirectionRot_mean_246  wtc_PowerRed_timeon   Epot  \\\n", "100086                          33.04                 0.00 301.68   \n", "200310                          33.04                 0.00 301.68   \n", "262950                          33.04                 0.00 301.68   \n", "279654                          33.04                 0.00 301.68   \n", "429990                          33.04                 0.00 301.68   \n", "438342                          33.04                 0.00 301.68   \n", "534390                          33.04                 0.00 301.68   \n", "\n", "        Correction Factor  Available Turbines     EL  ELX   ELNX  EL_indefini  \\\n", "100086               1.00              124.00 301.68 0.00 301.68         0.00   \n", "200310               1.00              124.00 301.68 0.00 301.68         0.00   \n", "262950               1.00              124.00 301.68 0.00 301.68         0.00   \n", "279654               1.00              124.00 301.68 0.00 301.68         0.00   \n", "429990               1.00              124.00 151.68 0.00   0.00       151.68   \n", "438342               1.00              124.00 301.68 0.00 301.68         0.00   \n", "534390               1.00              124.00 301.68 0.00 301.68         0.00   \n", "\n", "        prev_AcWindSp  next_AcWindSp  prev_ActPower_min  next_ActPower_min  \\\n", "100086           9.45          10.23            1475.00            1283.00   \n", "200310          10.15           7.95            1898.00             844.00   \n", "262950           9.46           7.62            1149.00             706.00   \n", "279654           8.06           9.23             773.00            1035.00   \n", "429990           9.81           7.78            1453.00             715.00   \n", "438342           7.78           8.29             715.00             866.00   \n", "534390           8.88           8.31             856.00             786.00   \n", "\n", "        prev_Alarme  next_Alarme  DiffV1  DiffV2  EL_PowerRed  EL_2006  \\\n", "100086         0.00         0.00    1.38    2.15         0.00     0.00   \n", "200310         0.00         0.00   10.15    7.95         0.00     0.00   \n", "262950         0.00         0.00    1.24   -0.60         0.00     0.00   \n", "279654         0.00         0.00    8.06    9.23         0.00     0.00   \n", "429990         0.00         0.00    2.76    0.72         0.00     0.00   \n", "438342         0.00         0.00    1.58    2.10         0.00     0.00   \n", "534390         0.00         0.00    2.72    2.15         0.00     0.00   \n", "\n", "        EL_wind  Duration lowind(s)  EL_wind_start  Duration lowind_start(s)  \\\n", "100086      NaN                 NaN            NaN                       NaN   \n", "200310      NaN                 NaN            NaN                       NaN   \n", "262950      NaN                 NaN            NaN                       NaN   \n", "279654      NaN                 NaN            NaN                       NaN   \n", "429990   151.68              600.00            NaN                       NaN   \n", "438342      NaN                 NaN            NaN                       NaN   \n", "534390      NaN                 NaN            NaN                       NaN   \n", "\n", "        EL_alarm_start  Duration alarm_start(s)  EL_indefini_left  \\\n", "100086             NaN                      NaN              0.00   \n", "200310             NaN                      NaN              0.00   \n", "262950             NaN                      NaN              0.00   \n", "279654             NaN                      NaN              0.00   \n", "429990             NaN                      NaN              0.00   \n", "438342             NaN                      NaN              0.00   \n", "534390             NaN                      NaN              0.00   \n", "\n", "        EL_Misassigned  \n", "100086            0.00  \n", "200310            0.00  \n", "262950            0.00  \n", "279654            0.00  \n", "429990            0.00  \n", "438342            0.00  \n", "534390            0.00  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_time = pd.to_datetime(\"2024-02-29 01:10:00\")\n", "\n", "results.query(\"TimeStamp == @current_time & EL > 0\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>StationId</th>\n", "      <th>TimeStamp</th>\n", "      <th>RealPeriod</th>\n", "      <th>Period 0(s)</th>\n", "      <th>Period 1(s)</th>\n", "      <th>UK Text</th>\n", "      <th>Duration 2006(s)</th>\n", "      <th>wtc_kWG1Tot_accum</th>\n", "      <th>wtc_kWG1TotE_accum</th>\n", "      <th>wtc_kWG1TotI_accum</th>\n", "      <th>wtc_ActPower_min</th>\n", "      <th>wtc_ActPower_max</th>\n", "      <th>wtc_ActPower_mean</th>\n", "      <th>wtc_AcWindSp_mean</th>\n", "      <th>wtc_ActualWindDirection_mean</th>\n", "      <th>met_WindSpeedRot_mean_38</th>\n", "      <th>met_WindSpeedRot_mean_39</th>\n", "      <th>met_WindSpeedRot_mean_246</th>\n", "      <th>met_WinddirectionRot_mean_38</th>\n", "      <th>met_WinddirectionRot_mean_39</th>\n", "      <th>met_WinddirectionRot_mean_246</th>\n", "      <th>wtc_PowerRed_timeon</th>\n", "      <th>Epot</th>\n", "      <th>Correction Factor</th>\n", "      <th>Available Turbines</th>\n", "      <th>EL</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>EL_indefini</th>\n", "      <th>prev_AcWindSp</th>\n", "      <th>next_AcWindSp</th>\n", "      <th>prev_ActPower_min</th>\n", "      <th>next_ActPower_min</th>\n", "      <th>prev_Alarme</th>\n", "      <th>next_<PERSON><PERSON>e</th>\n", "      <th>DiffV1</th>\n", "      <th>DiffV2</th>\n", "      <th>EL_PowerRed</th>\n", "      <th>EL_2006</th>\n", "      <th>EL_wind</th>\n", "      <th>Duration lowind(s)</th>\n", "      <th>EL_wind_start</th>\n", "      <th>Duration lowind_start(s)</th>\n", "      <th>EL_alarm_start</th>\n", "      <th>Duration alarm_start(s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>429989</th>\n", "      <td>103</td>\n", "      <td>2024-02-29 01:00:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>157.00</td>\n", "      <td>157.00</td>\n", "      <td>0.00</td>\n", "      <td>312.00</td>\n", "      <td>1680.00</td>\n", "      <td>943.54</td>\n", "      <td>7.71</td>\n", "      <td>42.50</td>\n", "      <td>9.97</td>\n", "      <td>9.96</td>\n", "      <td>9.59</td>\n", "      <td>44.49</td>\n", "      <td>42.63</td>\n", "      <td>33.90</td>\n", "      <td>0.00</td>\n", "      <td>157.00</td>\n", "      <td>1.00</td>\n", "      <td>125.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>8.81</td>\n", "      <td>6.83</td>\n", "      <td>991.00</td>\n", "      <td>490.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1.10</td>\n", "      <td>-0.88</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        StationId           TimeStamp  RealPeriod  Period 0(s)  Period 1(s)  \\\n", "429989        103 2024-02-29 01:00:00        0.00         0.00         0.00   \n", "\n", "       UK Text  Duration 2006(s)  wtc_kWG1Tot_accum  wtc_kWG1TotE_accum  \\\n", "429989       0              0.00             157.00              157.00   \n", "\n", "        wtc_kWG1TotI_accum  wtc_ActPower_min  wtc_ActPower_max  \\\n", "429989                0.00            312.00           1680.00   \n", "\n", "        wtc_ActPower_mean  wtc_AcWindSp_mean  wtc_ActualWindDirection_mean  \\\n", "429989             943.54               7.71                         42.50   \n", "\n", "        met_WindSpeedRot_mean_38  met_WindSpeedRot_mean_39  \\\n", "429989                      9.97                      9.96   \n", "\n", "        met_WindSpeedRot_mean_246  met_WinddirectionRot_mean_38  \\\n", "429989                       9.59                         44.49   \n", "\n", "        met_WinddirectionRot_mean_39  met_WinddirectionRot_mean_246  \\\n", "429989                         42.63                          33.90   \n", "\n", "        wtc_PowerRed_timeon   Epot  Correction Factor  Available Turbines  \\\n", "429989                 0.00 157.00               1.00              125.00   \n", "\n", "         EL  ELX  ELNX  EL_indefini  prev_AcWindSp  next_AcWindSp  \\\n", "429989 0.00 0.00  0.00         0.00           8.81           6.83   \n", "\n", "        prev_ActPower_min  next_ActPower_min  prev_<PERSON><PERSON>e  next_Alarme  \\\n", "429989             991.00             490.00         0.00         0.00   \n", "\n", "        DiffV1  DiffV2  EL_PowerRed  EL_2006  EL_wind  Duration lowind(s)  \\\n", "429989    1.10   -0.88         0.00     0.00      NaN                 NaN   \n", "\n", "        EL_wind_start  Duration lowind_start(s)  EL_alarm_start  \\\n", "429989            NaN                       NaN             NaN   \n", "\n", "        Duration alarm_start(s)  EL_indefini_left  EL_Misassigned  \n", "429989                      NaN              0.00            0.00  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["current_time = pd.to_datetime(\"2024-02-29 01:00:00\")\n", "\n", "results.query(\"TimeStamp == @current_time & StationId == 103\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PEP wtc_kWG1TotE_accum: 17063.341796875\n", "PEP wtc_ActPower_mean: 17063.900390625\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TimeStamp</th>\n", "      <th>Available_Turbines</th>\n", "      <th>wtc_ActPower_mean</th>\n", "      <th>wtc_kWG1TotE_accum</th>\n", "      <th>PEP wtc_kWG1TotE_accum</th>\n", "      <th>PEP wtc_ActPower_mean</th>\n", "      <th>PEP internal</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-02-29 00:10:00</td>\n", "      <td>125.00</td>\n", "      <td>44752.84</td>\n", "      <td>44762.00</td>\n", "      <td>356.66</td>\n", "      <td>356.59</td>\n", "      <td>356.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-02-29 00:20:00</td>\n", "      <td>125.00</td>\n", "      <td>42387.90</td>\n", "      <td>42404.00</td>\n", "      <td>337.88</td>\n", "      <td>337.75</td>\n", "      <td>337.88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-02-29 00:30:00</td>\n", "      <td>124.00</td>\n", "      <td>36830.64</td>\n", "      <td>36850.00</td>\n", "      <td>295.79</td>\n", "      <td>295.64</td>\n", "      <td>295.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-02-29 00:40:00</td>\n", "      <td>125.00</td>\n", "      <td>31861.41</td>\n", "      <td>31877.00</td>\n", "      <td>254.00</td>\n", "      <td>253.87</td>\n", "      <td>254.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-02-29 00:50:00</td>\n", "      <td>125.00</td>\n", "      <td>32261.63</td>\n", "      <td>32251.00</td>\n", "      <td>256.98</td>\n", "      <td>257.06</td>\n", "      <td>256.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-02-29 01:00:00</td>\n", "      <td>125.00</td>\n", "      <td>34493.60</td>\n", "      <td>34473.00</td>\n", "      <td>274.68</td>\n", "      <td>274.85</td>\n", "      <td>274.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>124.00</td>\n", "      <td>37589.05</td>\n", "      <td>37583.00</td>\n", "      <td>301.68</td>\n", "      <td>301.72</td>\n", "      <td>301.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-02-29 01:20:00</td>\n", "      <td>125.00</td>\n", "      <td>39375.67</td>\n", "      <td>39358.00</td>\n", "      <td>313.61</td>\n", "      <td>313.75</td>\n", "      <td>313.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-02-29 01:30:00</td>\n", "      <td>125.00</td>\n", "      <td>41177.49</td>\n", "      <td>41182.00</td>\n", "      <td>328.14</td>\n", "      <td>328.10</td>\n", "      <td>328.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-02-29 01:40:00</td>\n", "      <td>124.00</td>\n", "      <td>36816.86</td>\n", "      <td>36837.00</td>\n", "      <td>295.69</td>\n", "      <td>295.53</td>\n", "      <td>295.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-02-29 01:50:00</td>\n", "      <td>124.00</td>\n", "      <td>34526.34</td>\n", "      <td>34533.00</td>\n", "      <td>277.19</td>\n", "      <td>277.14</td>\n", "      <td>277.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-02-29 02:00:00</td>\n", "      <td>123.00</td>\n", "      <td>33771.35</td>\n", "      <td>33751.00</td>\n", "      <td>272.94</td>\n", "      <td>273.10</td>\n", "      <td>272.94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-02-29 02:10:00</td>\n", "      <td>124.00</td>\n", "      <td>34092.39</td>\n", "      <td>34106.00</td>\n", "      <td>273.77</td>\n", "      <td>273.66</td>\n", "      <td>273.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-02-29 02:20:00</td>\n", "      <td>120.00</td>\n", "      <td>32234.73</td>\n", "      <td>32251.00</td>\n", "      <td>266.80</td>\n", "      <td>266.66</td>\n", "      <td>266.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-02-29 02:30:00</td>\n", "      <td>122.00</td>\n", "      <td>27569.98</td>\n", "      <td>27584.00</td>\n", "      <td>224.75</td>\n", "      <td>224.63</td>\n", "      <td>224.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-02-29 02:40:00</td>\n", "      <td>123.00</td>\n", "      <td>24793.91</td>\n", "      <td>24797.00</td>\n", "      <td>200.53</td>\n", "      <td>200.50</td>\n", "      <td>200.53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-02-29 02:50:00</td>\n", "      <td>122.00</td>\n", "      <td>24664.48</td>\n", "      <td>24666.00</td>\n", "      <td>200.97</td>\n", "      <td>200.96</td>\n", "      <td>200.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-02-29 03:00:00</td>\n", "      <td>122.00</td>\n", "      <td>22429.67</td>\n", "      <td>22449.00</td>\n", "      <td>182.91</td>\n", "      <td>182.75</td>\n", "      <td>182.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-02-29 03:10:00</td>\n", "      <td>116.00</td>\n", "      <td>20456.66</td>\n", "      <td>20453.00</td>\n", "      <td>174.57</td>\n", "      <td>174.60</td>\n", "      <td>174.57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-02-29 03:20:00</td>\n", "      <td>118.00</td>\n", "      <td>19896.15</td>\n", "      <td>19892.00</td>\n", "      <td>167.12</td>\n", "      <td>167.16</td>\n", "      <td>167.12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             TimeStamp  Available_Turbines  wtc_ActPower_mean  \\\n", "0  2024-02-29 00:10:00              125.00           44752.84   \n", "1  2024-02-29 00:20:00              125.00           42387.90   \n", "2  2024-02-29 00:30:00              124.00           36830.64   \n", "3  2024-02-29 00:40:00              125.00           31861.41   \n", "4  2024-02-29 00:50:00              125.00           32261.63   \n", "5  2024-02-29 01:00:00              125.00           34493.60   \n", "6  2024-02-29 01:10:00              124.00           37589.05   \n", "7  2024-02-29 01:20:00              125.00           39375.67   \n", "8  2024-02-29 01:30:00              125.00           41177.49   \n", "9  2024-02-29 01:40:00              124.00           36816.86   \n", "10 2024-02-29 01:50:00              124.00           34526.34   \n", "11 2024-02-29 02:00:00              123.00           33771.35   \n", "12 2024-02-29 02:10:00              124.00           34092.39   \n", "13 2024-02-29 02:20:00              120.00           32234.73   \n", "14 2024-02-29 02:30:00              122.00           27569.98   \n", "15 2024-02-29 02:40:00              123.00           24793.91   \n", "16 2024-02-29 02:50:00              122.00           24664.48   \n", "17 2024-02-29 03:00:00              122.00           22429.67   \n", "18 2024-02-29 03:10:00              116.00           20456.66   \n", "19 2024-02-29 03:20:00              118.00           19896.15   \n", "\n", "    wtc_kWG1TotE_accum  PEP wtc_kWG1TotE_accum  PEP wtc_ActPower_mean  \\\n", "0             44762.00                  356.66                 356.59   \n", "1             42404.00                  337.88                 337.75   \n", "2             36850.00                  295.79                 295.64   \n", "3             31877.00                  254.00                 253.87   \n", "4             32251.00                  256.98                 257.06   \n", "5             34473.00                  274.68                 274.85   \n", "6             37583.00                  301.68                 301.72   \n", "7             39358.00                  313.61                 313.75   \n", "8             41182.00                  328.14                 328.10   \n", "9             36837.00                  295.69                 295.53   \n", "10            34533.00                  277.19                 277.14   \n", "11            33751.00                  272.94                 273.10   \n", "12            34106.00                  273.77                 273.66   \n", "13            32251.00                  266.80                 266.66   \n", "14            27584.00                  224.75                 224.63   \n", "15            24797.00                  200.53                 200.50   \n", "16            24666.00                  200.97                 200.96   \n", "17            22449.00                  182.91                 182.75   \n", "18            20453.00                  174.57                 174.60   \n", "19            19892.00                  167.12                 167.16   \n", "\n", "    PEP internal  \n", "0         356.66  \n", "1         337.88  \n", "2         295.79  \n", "3         254.00  \n", "4         256.98  \n", "5         274.68  \n", "6         301.68  \n", "7         313.61  \n", "8         328.14  \n", "9         295.69  \n", "10        277.19  \n", "11        272.94  \n", "12        273.77  \n", "13        266.80  \n", "14        224.75  \n", "15        200.53  \n", "16        200.97  \n", "17        182.91  \n", "18        174.57  \n", "19        167.12  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Alarms and actpower min\n", "\n", "from datetime import timedelta\n", "\n", "pd.set_option(\"display.float_format\", \"{:.2f}\".format)\n", "# Set the start and end of the day\n", "start_date = pd.to_datetime(\"2024-02-29 00:10:00\")\n", "end_date = pd.to_datetime(\"2024-02-29 11:10:00\")\n", "\n", "# Initialize a list to store the rows\n", "rows = []\n", "\n", "# Loop through each timestamp in the day\n", "current_time = start_date\n", "while current_time < end_date:\n", "    query_result = results.query(\"TimeStamp == @current_time & RealPeriod == 0 & wtc_ActPower_min >= 0\")\n", "\n", "    if not query_result.empty:\n", "        Available_Turbines = query_result[\"Available Turbines\"].iloc[0]\n", "        wtc_ActPower_mean = query_result.wtc_ActPower_mean.sum() / 6\n", "        wtc_kWG1TotE_accum = query_result.wtc_kWG1TotE_accum.sum()\n", "        PEP_internal = results.query(\"TimeStamp == @current_time & StationId == 24\").Epot.values[0]\n", "        # Calculate the first value\n", "        value1 = round(CF(Available_Turbines) * wtc_kWG1TotE_accum / Available_Turbines, 2)\n", "\n", "        # Calculate the second value\n", "        value2 = round(CF(Available_Turbines) * wtc_ActPower_mean / Available_Turbines, 2)\n", "\n", "        # Append the row to our list\n", "        rows.append(\n", "            {\n", "                \"TimeStamp\": current_time,\n", "                \"Available_Turbines\": Available_Turbines,\n", "                \"wtc_ActPower_mean\": wtc_ActPower_mean,\n", "                \"wtc_kWG1TotE_accum\": wtc_kWG1TotE_accum,\n", "                \"PEP wtc_kWG1TotE_accum\": value1,\n", "                \"PEP wtc_ActPower_mean\": value2,\n", "                \"PEP internal\": PEP_internal,\n", "            }\n", "        )\n", "\n", "    # Move to the next timestamp (assuming 10-minute intervals)\n", "    current_time += <PERSON><PERSON><PERSON>(minutes=10)\n", "\n", "# Convert the list of rows to a DataFrame\n", "result_df = pd.DataFrame(rows)\n", "\n", "# Calculate the sums\n", "sum_value1 = result_df[\"PEP wtc_kWG1TotE_accum\"].sum()\n", "sum_value2 = result_df[\"PEP wtc_ActPower_mean\"].sum()\n", "\n", "# Print the results\n", "print(f\"PEP wtc_kWG1TotE_accum: {sum_value1}\")\n", "print(f\"PEP wtc_ActPower_mean: {sum_value2}\")\n", "\n", "result_df.head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PEP wtc_kWG1TotE_accum: 5248.83056640625\n", "PEP wtc_ActPower_mean: 5248.2001953125\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TimeStamp</th>\n", "      <th>Available_Turbines</th>\n", "      <th>wtc_ActPower_mean</th>\n", "      <th>wtc_kWG1TotE_accum</th>\n", "      <th>PEP wtc_kWG1TotE_accum</th>\n", "      <th>PEP wtc_ActPower_mean</th>\n", "      <th>PEP internal</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-02-29 00:10:00</td>\n", "      <td>125</td>\n", "      <td>44752.84</td>\n", "      <td>44762.00</td>\n", "      <td>356.66</td>\n", "      <td>356.59</td>\n", "      <td>356.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-02-29 00:20:00</td>\n", "      <td>125</td>\n", "      <td>42387.90</td>\n", "      <td>42404.00</td>\n", "      <td>337.88</td>\n", "      <td>337.75</td>\n", "      <td>337.88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-02-29 00:30:00</td>\n", "      <td>125</td>\n", "      <td>36991.20</td>\n", "      <td>37010.00</td>\n", "      <td>294.90</td>\n", "      <td>294.75</td>\n", "      <td>295.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-02-29 00:40:00</td>\n", "      <td>125</td>\n", "      <td>31861.41</td>\n", "      <td>31877.00</td>\n", "      <td>254.00</td>\n", "      <td>253.87</td>\n", "      <td>254.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-02-29 00:50:00</td>\n", "      <td>125</td>\n", "      <td>32261.63</td>\n", "      <td>32251.00</td>\n", "      <td>256.98</td>\n", "      <td>257.06</td>\n", "      <td>256.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-02-29 01:00:00</td>\n", "      <td>125</td>\n", "      <td>34493.60</td>\n", "      <td>34473.00</td>\n", "      <td>274.68</td>\n", "      <td>274.85</td>\n", "      <td>274.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-02-29 01:10:00</td>\n", "      <td>125</td>\n", "      <td>37738.71</td>\n", "      <td>37733.00</td>\n", "      <td>300.66</td>\n", "      <td>300.70</td>\n", "      <td>301.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2024-02-29 01:20:00</td>\n", "      <td>125</td>\n", "      <td>39375.67</td>\n", "      <td>39358.00</td>\n", "      <td>313.61</td>\n", "      <td>313.75</td>\n", "      <td>313.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2024-02-29 01:30:00</td>\n", "      <td>125</td>\n", "      <td>41177.49</td>\n", "      <td>41182.00</td>\n", "      <td>328.14</td>\n", "      <td>328.10</td>\n", "      <td>328.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2024-02-29 01:40:00</td>\n", "      <td>124</td>\n", "      <td>36816.86</td>\n", "      <td>36837.00</td>\n", "      <td>295.69</td>\n", "      <td>295.53</td>\n", "      <td>295.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2024-02-29 01:50:00</td>\n", "      <td>124</td>\n", "      <td>34526.34</td>\n", "      <td>34533.00</td>\n", "      <td>277.19</td>\n", "      <td>277.14</td>\n", "      <td>277.19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2024-02-29 02:00:00</td>\n", "      <td>124</td>\n", "      <td>33873.31</td>\n", "      <td>33853.00</td>\n", "      <td>271.74</td>\n", "      <td>271.90</td>\n", "      <td>272.94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-02-29 02:10:00</td>\n", "      <td>124</td>\n", "      <td>34092.39</td>\n", "      <td>34106.00</td>\n", "      <td>273.77</td>\n", "      <td>273.66</td>\n", "      <td>273.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-02-29 02:20:00</td>\n", "      <td>121</td>\n", "      <td>32380.71</td>\n", "      <td>32397.00</td>\n", "      <td>265.96</td>\n", "      <td>265.83</td>\n", "      <td>266.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-02-29 02:30:00</td>\n", "      <td>124</td>\n", "      <td>27793.03</td>\n", "      <td>27806.00</td>\n", "      <td>223.20</td>\n", "      <td>223.09</td>\n", "      <td>224.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-02-29 02:40:00</td>\n", "      <td>124</td>\n", "      <td>24815.62</td>\n", "      <td>24819.00</td>\n", "      <td>199.22</td>\n", "      <td>199.19</td>\n", "      <td>200.53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-02-29 02:50:00</td>\n", "      <td>123</td>\n", "      <td>24723.21</td>\n", "      <td>24725.00</td>\n", "      <td>199.95</td>\n", "      <td>199.93</td>\n", "      <td>200.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-02-29 03:00:00</td>\n", "      <td>122</td>\n", "      <td>22429.67</td>\n", "      <td>22449.00</td>\n", "      <td>182.91</td>\n", "      <td>182.75</td>\n", "      <td>182.91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-02-29 03:10:00</td>\n", "      <td>116</td>\n", "      <td>20456.66</td>\n", "      <td>20453.00</td>\n", "      <td>174.57</td>\n", "      <td>174.60</td>\n", "      <td>174.57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-02-29 03:20:00</td>\n", "      <td>118</td>\n", "      <td>19896.15</td>\n", "      <td>19892.00</td>\n", "      <td>167.12</td>\n", "      <td>167.16</td>\n", "      <td>167.12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             TimeStamp  Available_Turbines  wtc_ActPower_mean  \\\n", "0  2024-02-29 00:10:00                 125           44752.84   \n", "1  2024-02-29 00:20:00                 125           42387.90   \n", "2  2024-02-29 00:30:00                 125           36991.20   \n", "3  2024-02-29 00:40:00                 125           31861.41   \n", "4  2024-02-29 00:50:00                 125           32261.63   \n", "5  2024-02-29 01:00:00                 125           34493.60   \n", "6  2024-02-29 01:10:00                 125           37738.71   \n", "7  2024-02-29 01:20:00                 125           39375.67   \n", "8  2024-02-29 01:30:00                 125           41177.49   \n", "9  2024-02-29 01:40:00                 124           36816.86   \n", "10 2024-02-29 01:50:00                 124           34526.34   \n", "11 2024-02-29 02:00:00                 124           33873.31   \n", "12 2024-02-29 02:10:00                 124           34092.39   \n", "13 2024-02-29 02:20:00                 121           32380.71   \n", "14 2024-02-29 02:30:00                 124           27793.03   \n", "15 2024-02-29 02:40:00                 124           24815.62   \n", "16 2024-02-29 02:50:00                 123           24723.21   \n", "17 2024-02-29 03:00:00                 122           22429.67   \n", "18 2024-02-29 03:10:00                 116           20456.66   \n", "19 2024-02-29 03:20:00                 118           19896.15   \n", "\n", "    wtc_kWG1TotE_accum  PEP wtc_kWG1TotE_accum  PEP wtc_ActPower_mean  \\\n", "0             44762.00                  356.66                 356.59   \n", "1             42404.00                  337.88                 337.75   \n", "2             37010.00                  294.90                 294.75   \n", "3             31877.00                  254.00                 253.87   \n", "4             32251.00                  256.98                 257.06   \n", "5             34473.00                  274.68                 274.85   \n", "6             37733.00                  300.66                 300.70   \n", "7             39358.00                  313.61                 313.75   \n", "8             41182.00                  328.14                 328.10   \n", "9             36837.00                  295.69                 295.53   \n", "10            34533.00                  277.19                 277.14   \n", "11            33853.00                  271.74                 271.90   \n", "12            34106.00                  273.77                 273.66   \n", "13            32397.00                  265.96                 265.83   \n", "14            27806.00                  223.20                 223.09   \n", "15            24819.00                  199.22                 199.19   \n", "16            24725.00                  199.95                 199.93   \n", "17            22449.00                  182.91                 182.75   \n", "18            20453.00                  174.57                 174.60   \n", "19            19892.00                  167.12                 167.16   \n", "\n", "    PEP internal  \n", "0         356.66  \n", "1         337.88  \n", "2         295.79  \n", "3         254.00  \n", "4         256.98  \n", "5         274.68  \n", "6         301.68  \n", "7         313.61  \n", "8         328.14  \n", "9         295.69  \n", "10        277.19  \n", "11        272.94  \n", "12        273.77  \n", "13        266.80  \n", "14        224.75  \n", "15        200.53  \n", "16        200.97  \n", "17        182.91  \n", "18        174.57  \n", "19        167.12  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Alarms Only\n", "\n", "from datetime import timedelta\n", "\n", "pd.set_option(\"display.float_format\", \"{:.2f}\".format)\n", "# Set the start and end of the day\n", "start_date = pd.to_datetime(\"2024-02-29 00:10:00\")\n", "end_date = pd.to_datetime(\"2024-02-29 03:30:00\")\n", "\n", "# Initialize a list to store the rows\n", "rows = []\n", "\n", "# Loop through each timestamp in the day\n", "current_time = start_date\n", "while current_time < end_date:\n", "    query_result = results.query(\"TimeStamp == @current_time & RealPeriod == 0\")\n", "\n", "    if not query_result.empty:\n", "        Available_Turbines = len(query_result)\n", "        wtc_ActPower_mean = query_result.wtc_ActPower_mean.sum() / 6\n", "        wtc_kWG1TotE_accum = query_result.wtc_kWG1TotE_accum.sum()\n", "        PEP_internal = results.query(\"TimeStamp == @current_time & StationId == 24\").Epot.values[0]\n", "        # Calculate the first value\n", "        value1 = round(CF(Available_Turbines) * wtc_kWG1TotE_accum / Available_Turbines, 2)\n", "\n", "        # Calculate the second value\n", "        value2 = round(CF(Available_Turbines) * wtc_ActPower_mean / Available_Turbines, 2)\n", "\n", "        # Append the row to our list\n", "        rows.append(\n", "            {\n", "                \"TimeStamp\": current_time,\n", "                \"Available_Turbines\": Available_Turbines,\n", "                \"wtc_ActPower_mean\": wtc_ActPower_mean,\n", "                \"wtc_kWG1TotE_accum\": wtc_kWG1TotE_accum,\n", "                \"PEP wtc_kWG1TotE_accum\": value1,\n", "                \"PEP wtc_ActPower_mean\": value2,\n", "                \"PEP internal\": PEP_internal,\n", "            }\n", "        )\n", "\n", "    # Move to the next timestamp (assuming 10-minute intervals)\n", "    current_time += <PERSON><PERSON><PERSON>(minutes=10)\n", "\n", "# Convert the list of rows to a DataFrame\n", "result_df = pd.DataFrame(rows)\n", "\n", "# Calculate the sums\n", "sum_value1 = result_df[\"PEP wtc_kWG1TotE_accum\"].sum()\n", "sum_value2 = result_df[\"PEP wtc_ActPower_mean\"].sum()\n", "\n", "# Print the results\n", "print(f\"PEP wtc_kWG1TotE_accum: {sum_value1}\")\n", "print(f\"PEP wtc_ActPower_mean: {sum_value2}\")\n", "\n", "result_df.head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float32(17062.283)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["results.query(\"@start_date <= TimeStamp < @end_date & StationId == 24\").Epot.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>StationId</th>\n", "      <th>TimeStamp</th>\n", "      <th>RealPeriod</th>\n", "      <th>Period 0(s)</th>\n", "      <th>Period 1(s)</th>\n", "      <th>UK Text</th>\n", "      <th>Duration 2006(s)</th>\n", "      <th>wtc_kWG1Tot_accum</th>\n", "      <th>wtc_kWG1TotE_accum</th>\n", "      <th>wtc_kWG1TotI_accum</th>\n", "      <th>wtc_ActPower_min</th>\n", "      <th>wtc_ActPower_max</th>\n", "      <th>wtc_ActPower_mean</th>\n", "      <th>wtc_AcWindSp_mean</th>\n", "      <th>wtc_ActualWindDirection_mean</th>\n", "      <th>met_WindSpeedRot_mean_38</th>\n", "      <th>met_WindSpeedRot_mean_39</th>\n", "      <th>met_WindSpeedRot_mean_246</th>\n", "      <th>met_WinddirectionRot_mean_38</th>\n", "      <th>met_WinddirectionRot_mean_39</th>\n", "      <th>met_WinddirectionRot_mean_246</th>\n", "      <th>wtc_PowerRed_timeon</th>\n", "      <th>Epot</th>\n", "      <th>Correction Factor</th>\n", "      <th>Available Turbines</th>\n", "      <th>EL</th>\n", "      <th>ELX</th>\n", "      <th>ELNX</th>\n", "      <th>EL_indefini</th>\n", "      <th>prev_AcWindSp</th>\n", "      <th>next_AcWindSp</th>\n", "      <th>prev_ActPower_min</th>\n", "      <th>next_ActPower_min</th>\n", "      <th>prev_Alarme</th>\n", "      <th>next_<PERSON><PERSON>e</th>\n", "      <th>DiffV1</th>\n", "      <th>DiffV2</th>\n", "      <th>EL_PowerRed</th>\n", "      <th>EL_2006</th>\n", "      <th>EL_wind</th>\n", "      <th>Duration lowind(s)</th>\n", "      <th>EL_wind_start</th>\n", "      <th>Duration lowind_start(s)</th>\n", "      <th>EL_alarm_start</th>\n", "      <th>Duration alarm_start(s)</th>\n", "      <th><PERSON><PERSON>_indefini_left</th>\n", "      <th><PERSON><PERSON>_Misassigned</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>100083</th>\n", "      <td>24</td>\n", "      <td>2024-02-29 00:40:00</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>600.0</td>\n", "      <td>Timeout  DC-circuit charging</td>\n", "      <td>0.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>-56.0</td>\n", "      <td>-6.0</td>\n", "      <td>-7.200000</td>\n", "      <td>5.73</td>\n", "      <td>30.459999</td>\n", "      <td>10.79</td>\n", "      <td>10.78</td>\n", "      <td>8.74</td>\n", "      <td>38.27</td>\n", "      <td>36.560001</td>\n", "      <td>32.650002</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>1.0</td>\n", "      <td>125.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>8.34</td>\n", "      <td>6.99</td>\n", "      <td>1030.0</td>\n", "      <td>541.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.61</td>\n", "      <td>1.27</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200307</th>\n", "      <td>48</td>\n", "      <td>2024-02-29 00:40:00</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>600.0</td>\n", "      <td>DC fuse blown</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.00</td>\n", "      <td>0.000000</td>\n", "      <td>10.79</td>\n", "      <td>10.78</td>\n", "      <td>8.74</td>\n", "      <td>38.27</td>\n", "      <td>36.560001</td>\n", "      <td>32.650002</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>1.0</td>\n", "      <td>125.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>7.90</td>\n", "      <td>7.58</td>\n", "      <td>755.0</td>\n", "      <td>536.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.90</td>\n", "      <td>7.58</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>262947</th>\n", "      <td>63</td>\n", "      <td>2024-02-29 00:40:00</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>600.0</td>\n", "      <td>Manual stop</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-4.0</td>\n", "      <td>-3.0</td>\n", "      <td>-3.910000</td>\n", "      <td>6.53</td>\n", "      <td>42.410000</td>\n", "      <td>10.79</td>\n", "      <td>10.78</td>\n", "      <td>8.74</td>\n", "      <td>38.27</td>\n", "      <td>36.560001</td>\n", "      <td>32.650002</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>1.0</td>\n", "      <td>125.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>7.36</td>\n", "      <td>7.50</td>\n", "      <td>658.0</td>\n", "      <td>752.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.83</td>\n", "      <td>0.97</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>279651</th>\n", "      <td>67</td>\n", "      <td>2024-02-29 00:40:00</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>600.0</td>\n", "      <td>Converter trip, external</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.00</td>\n", "      <td>0.000000</td>\n", "      <td>10.79</td>\n", "      <td>10.78</td>\n", "      <td>8.74</td>\n", "      <td>38.27</td>\n", "      <td>36.560001</td>\n", "      <td>32.650002</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>1.0</td>\n", "      <td>125.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>9.59</td>\n", "      <td>8.33</td>\n", "      <td>1248.0</td>\n", "      <td>796.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.59</td>\n", "      <td>8.33</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>438339</th>\n", "      <td>105</td>\n", "      <td>2024-02-29 00:40:00</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>600.0</td>\n", "      <td>Smoke in the A3 box</td>\n", "      <td>0.0</td>\n", "      <td>-1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>-2.0</td>\n", "      <td>-1.0</td>\n", "      <td>-1.800000</td>\n", "      <td>6.22</td>\n", "      <td>30.980000</td>\n", "      <td>10.79</td>\n", "      <td>10.78</td>\n", "      <td>8.74</td>\n", "      <td>38.27</td>\n", "      <td>36.560001</td>\n", "      <td>32.650002</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>1.0</td>\n", "      <td>125.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>7.42</td>\n", "      <td>7.55</td>\n", "      <td>635.0</td>\n", "      <td>556.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.20</td>\n", "      <td>1.34</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>534387</th>\n", "      <td>128</td>\n", "      <td>2024-02-29 00:40:00</td>\n", "      <td>600.0</td>\n", "      <td>0.0</td>\n", "      <td>600.0</td>\n", "      <td>Gear oil temperature high</td>\n", "      <td>0.0</td>\n", "      <td>-3.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>-37.0</td>\n", "      <td>-2.0</td>\n", "      <td>-17.040001</td>\n", "      <td>5.99</td>\n", "      <td>34.520000</td>\n", "      <td>10.79</td>\n", "      <td>10.78</td>\n", "      <td>8.74</td>\n", "      <td>38.27</td>\n", "      <td>36.560001</td>\n", "      <td>32.650002</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>1.0</td>\n", "      <td>125.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>254.0</td>\n", "      <td>0.0</td>\n", "      <td>8.60</td>\n", "      <td>8.18</td>\n", "      <td>850.0</td>\n", "      <td>639.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.61</td>\n", "      <td>2.20</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        StationId           TimeStamp  RealPeriod  Period 0(s)  Period 1(s)  \\\n", "100083         24 2024-02-29 00:40:00       600.0          0.0        600.0   \n", "200307         48 2024-02-29 00:40:00       600.0          0.0        600.0   \n", "262947         63 2024-02-29 00:40:00       600.0          0.0        600.0   \n", "279651         67 2024-02-29 00:40:00       600.0          0.0        600.0   \n", "438339        105 2024-02-29 00:40:00       600.0          0.0        600.0   \n", "534387        128 2024-02-29 00:40:00       600.0          0.0        600.0   \n", "\n", "                             UK Text  Duration 2006(s)  wtc_kWG1Tot_accum  \\\n", "100083  Timeout  DC-circuit charging               0.0               -1.0   \n", "200307                 DC fuse blown               0.0                0.0   \n", "262947                   Manual stop               0.0                0.0   \n", "279651      Converter trip, external               0.0                0.0   \n", "438339           Smoke in the A3 box               0.0               -1.0   \n", "534387     Gear oil temperature high               0.0               -3.0   \n", "\n", "        wtc_kWG1TotE_accum  wtc_kWG1TotI_accum  wtc_ActPower_min  \\\n", "100083                 0.0                 1.0             -56.0   \n", "200307                 0.0                 0.0               0.0   \n", "262947                 0.0                 0.0              -4.0   \n", "279651                 0.0                 0.0               0.0   \n", "438339                 0.0                 1.0              -2.0   \n", "534387                 0.0                 3.0             -37.0   \n", "\n", "        wtc_ActPower_max  wtc_ActPower_mean  wtc_AcWindSp_mean  \\\n", "100083              -6.0          -7.200000               5.73   \n", "200307               0.0           0.000000               0.00   \n", "262947              -3.0          -3.910000               6.53   \n", "279651               0.0           0.000000               0.00   \n", "438339              -1.0          -1.800000               6.22   \n", "534387              -2.0         -17.040001               5.99   \n", "\n", "        wtc_ActualWindDirection_mean  met_WindSpeedRot_mean_38  \\\n", "100083                     30.459999                     10.79   \n", "200307                      0.000000                     10.79   \n", "262947                     42.410000                     10.79   \n", "279651                      0.000000                     10.79   \n", "438339                     30.980000                     10.79   \n", "534387                     34.520000                     10.79   \n", "\n", "        met_WindSpeedRot_mean_39  met_WindSpeedRot_mean_246  \\\n", "100083                     10.78                       8.74   \n", "200307                     10.78                       8.74   \n", "262947                     10.78                       8.74   \n", "279651                     10.78                       8.74   \n", "438339                     10.78                       8.74   \n", "534387                     10.78                       8.74   \n", "\n", "        met_WinddirectionRot_mean_38  met_WinddirectionRot_mean_39  \\\n", "100083                         38.27                     36.560001   \n", "200307                         38.27                     36.560001   \n", "262947                         38.27                     36.560001   \n", "279651                         38.27                     36.560001   \n", "438339                         38.27                     36.560001   \n", "534387                         38.27                     36.560001   \n", "\n", "        met_WinddirectionRot_mean_246  wtc_PowerRed_timeon   Epot  \\\n", "100083                      32.650002                  0.0  254.0   \n", "200307                      32.650002                  0.0  254.0   \n", "262947                      32.650002                  0.0  254.0   \n", "279651                      32.650002                  0.0  254.0   \n", "438339                      32.650002                  0.0  254.0   \n", "534387                      32.650002                  0.0  254.0   \n", "\n", "        Correction Factor  Available Turbines     EL  ELX   ELNX  EL_indefini  \\\n", "100083                1.0               125.0  254.0  0.0  254.0          0.0   \n", "200307                1.0               125.0  254.0  0.0  254.0          0.0   \n", "262947                1.0               125.0  254.0  0.0  254.0          0.0   \n", "279651                1.0               125.0  254.0  0.0  254.0          0.0   \n", "438339                1.0               125.0  254.0  0.0  254.0          0.0   \n", "534387                1.0               125.0  254.0  0.0  254.0          0.0   \n", "\n", "        prev_AcWindSp  next_AcWindSp  prev_ActPower_min  next_ActPower_min  \\\n", "100083           8.34           6.99             1030.0              541.0   \n", "200307           7.90           7.58              755.0              536.0   \n", "262947           7.36           7.50              658.0              752.0   \n", "279651           9.59           8.33             1248.0              796.0   \n", "438339           7.42           7.55              635.0              556.0   \n", "534387           8.60           8.18              850.0              639.0   \n", "\n", "        prev_Alarme  next_Alarme  DiffV1  DiffV2  EL_PowerRed  EL_2006  \\\n", "100083          0.0          0.0    2.61    1.27          0.0      0.0   \n", "200307          0.0          0.0    7.90    7.58          0.0      0.0   \n", "262947          0.0          0.0    0.83    0.97          0.0      0.0   \n", "279651          0.0          0.0    9.59    8.33          0.0      0.0   \n", "438339          0.0          0.0    1.20    1.34          0.0      0.0   \n", "534387          0.0          0.0    2.61    2.20          0.0      0.0   \n", "\n", "        EL_wind  Duration lowind(s)  EL_wind_start  Duration lowind_start(s)  \\\n", "100083      NaN                 NaN            NaN                       NaN   \n", "200307      NaN                 NaN            NaN                       NaN   \n", "262947      NaN                 NaN            NaN                       NaN   \n", "279651      NaN                 NaN            NaN                       NaN   \n", "438339      NaN                 NaN            NaN                       NaN   \n", "534387      NaN                 NaN            NaN                       NaN   \n", "\n", "        EL_alarm_start  Duration alarm_start(s)  EL_indefini_left  \\\n", "100083             NaN                      NaN               0.0   \n", "200307             NaN                      NaN               0.0   \n", "262947             NaN                      NaN               0.0   \n", "279651             NaN                      NaN               0.0   \n", "438339             NaN                      NaN               0.0   \n", "534387             NaN                      NaN               0.0   \n", "\n", "        EL_Misassigned  \n", "100083             0.0  \n", "200307             0.0  \n", "262947             0.0  \n", "279651             0.0  \n", "438339             0.0  \n", "534387             0.0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["results.query(\"TimeStamp == @desired_datetime & RealPeriod != 0\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["(114494860.0, 114494760.0)"]}, "metadata": {}, "output_type": "display_data"}], "source": ["(\n", "    results.query(\"wtc_kWG1TotI_accum == 0\").wtc_kWG1Tot_accum.sum(),\n", "    results.query(\"wtc_kWG1TotI_accum == 0\").wtc_kWG1TotE_accum.sum(),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Analyze and compare scraped DATA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>YBA</th>\n", "      <th>EL</th>\n", "      <th>ELX</th>\n", "      <th>ELR</th>\n", "      <th>ELXR</th>\n", "      <th>PEP</th>\n", "      <th>OrgPEP</th>\n", "      <th>StandStill</th>\n", "      <th>Grid</th>\n", "      <th>Wind</th>\n", "      <th>Other</th>\n", "      <th>Error</th>\n", "      <th>Service</th>\n", "      <th>PM</th>\n", "      <th>PEPVar</th>\n", "      <th>ELVar</th>\n", "      <th>PMVar</th>\n", "      <th>ActLimit</th>\n", "      <th>Duration</th>\n", "      <th>PowerRed</th>\n", "      <th>VariantName</th>\n", "      <th>ELXC</th>\n", "      <th>PowerBoost</th>\n", "      <th>WAEP</th>\n", "      <th>ELGL</th>\n", "      <th>ELXGL</th>\n", "      <th>StationID</th>\n", "      <th>ParkID</th>\n", "      <th>TimestampStart</th>\n", "      <th>TimestampStartRaw</th>\n", "      <th>SS</th>\n", "      <th>GD</th>\n", "      <th>WD</th>\n", "      <th>OD</th>\n", "      <th>ER</th>\n", "      <th>SD</th>\n", "      <th>DD</th>\n", "      <th>ProductionG1</th>\n", "      <th>ProductionG2</th>\n", "      <th>ActPower</th>\n", "      <th>ActPowerValue</th>\n", "      <th>ProductionSource</th>\n", "      <th>ProductionSourceID</th>\n", "      <th>OrgProductionSourceID</th>\n", "      <th>AP</th>\n", "      <th>ChangePending</th>\n", "      <th>ChangedBy</th>\n", "      <th>ChangedDate</th>\n", "      <th>ChangedDateRaw</th>\n", "      <th>ProductionWarningID</th>\n", "      <th>ProductionWarning</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>65.68</td>\n", "      <td>65.68</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 00:00:00</td>\n", "      <td>2024/11/01 00:00:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108482448</td>\n", "      <td>0</td>\n", "      <td>65.68</td>\n", "      <td>65.68</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>65.68</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>62.29</td>\n", "      <td>62.29</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 00:10:00</td>\n", "      <td>2024/11/01 00:10:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108482510</td>\n", "      <td>0</td>\n", "      <td>62.29</td>\n", "      <td>62.29</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>62.29</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>64.74</td>\n", "      <td>64.74</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 00:20:00</td>\n", "      <td>2024/11/01 00:20:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108482575</td>\n", "      <td>0</td>\n", "      <td>64.74</td>\n", "      <td>64.74</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>64.74</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>56.31</td>\n", "      <td>56.31</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 00:30:00</td>\n", "      <td>2024/11/01 00:30:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108482631</td>\n", "      <td>0</td>\n", "      <td>56.31</td>\n", "      <td>56.31</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>56.31</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>54.79</td>\n", "      <td>54.79</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 00:40:00</td>\n", "      <td>2024/11/01 00:40:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108482686</td>\n", "      <td>0</td>\n", "      <td>54.79</td>\n", "      <td>54.79</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>54.79</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>570235</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>18.40</td>\n", "      <td>18.40</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>30/11/2024 23:10:00</td>\n", "      <td>2024/11/30 23:10:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73799071</td>\n", "      <td>0</td>\n", "      <td>18.40</td>\n", "      <td>18.40</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>18.40</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>570236</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>22.77</td>\n", "      <td>22.77</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>30/11/2024 23:20:00</td>\n", "      <td>2024/11/30 23:20:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73799093</td>\n", "      <td>0</td>\n", "      <td>22.77</td>\n", "      <td>22.77</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>22.77</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>570237</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>13.14</td>\n", "      <td>13.14</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>30/11/2024 23:30:00</td>\n", "      <td>2024/11/30 23:30:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73799107</td>\n", "      <td>0</td>\n", "      <td>13.14</td>\n", "      <td>13.14</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>13.14</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>570238</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>11.81</td>\n", "      <td>11.81</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>30/11/2024 23:40:00</td>\n", "      <td>2024/11/30 23:40:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73799118</td>\n", "      <td>0</td>\n", "      <td>11.81</td>\n", "      <td>11.81</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>11.81</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>570239</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>8.27</td>\n", "      <td>8.27</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>30/11/2024 23:50:00</td>\n", "      <td>2024/11/30 23:50:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73799127</td>\n", "      <td>0</td>\n", "      <td>8.27</td>\n", "      <td>8.27</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>8.27</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>2024/12/10 10:21:01</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>570240 rows × 51 columns</p>\n", "</div>"], "text/plain": ["           YBA    EL   ELX  ELR  ELXR    PEP OrgPEP  StandStill  Grid  Wind  \\\n", "0       100.00  0.00  0.00    0  0.00  65.68  65.68           0     0     0   \n", "1       100.00  0.00  0.00    0  0.00  62.29  62.29           0     0     0   \n", "2       100.00  0.00  0.00    0  0.00  64.74  64.74           0     0     0   \n", "3       100.00  0.00  0.00    0  0.00  56.31  56.31           0     0     0   \n", "4       100.00  0.00  0.00    0  0.00  54.79  54.79           0     0     0   \n", "...        ...   ...   ...  ...   ...    ...    ...         ...   ...   ...   \n", "570235  100.00  0.00  0.00    0  0.00  18.40  18.40           0     0     0   \n", "570236  100.00  0.00  0.00    0  0.00  22.77  22.77           0     0     0   \n", "570237  100.00  0.00  0.00    0  0.00  13.14  13.14           0     0     0   \n", "570238  100.00  0.00  0.00    0  0.00  11.81  11.81           0     0     0   \n", "570239  100.00  0.00  0.00    0  0.00   8.27   8.27           0     0     0   \n", "\n", "        Other  Error  Service  PM  PEPVar  ELVar  PMVar  ActLimit Duration  \\\n", "0           0      0        0   1       0      1      1         0     0.00   \n", "1           0      0        0   1       0      1      1         0     0.00   \n", "2           0      0        0   1       0      1      1         0     0.00   \n", "3           0      0        0   1       0      1      1         0     0.00   \n", "4           0      0        0   1       0      1      1         0     0.00   \n", "...       ...    ...      ...  ..     ...    ...    ...       ...      ...   \n", "570235      0      0        0   1       0      1      1         0     0.00   \n", "570236      0      0        0   1       0      1      1         0     0.00   \n", "570237      0      0        0   1       0      1      1         0     0.00   \n", "570238      0      0        0   1       0      1      1         0     0.00   \n", "570239      0      0        0   1       0      1      1         0     0.00   \n", "\n", "       PowerRed         VariantName  ELXC  PowerBoost  WAEP  ELGL  ELXGL  \\\n", "0       2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "1       2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "2       2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "3       2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "4       2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "...         ...                 ...   ...         ...   ...   ...    ...   \n", "570235  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "570236  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "570237  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "570238  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "570239  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "\n", "        StationID  ParkID       TimestampStart    TimestampStartRaw    SS  \\\n", "0         2307405       0   1/11/2024 00:00:00  2024/11/01 00:00:00  0.00   \n", "1         2307405       0   1/11/2024 00:10:00  2024/11/01 00:10:00  0.00   \n", "2         2307405       0   1/11/2024 00:20:00  2024/11/01 00:20:00  0.00   \n", "3         2307405       0   1/11/2024 00:30:00  2024/11/01 00:30:00  0.00   \n", "4         2307405       0   1/11/2024 00:40:00  2024/11/01 00:40:00  0.00   \n", "...           ...     ...                  ...                  ...   ...   \n", "570235    2307536       0  30/11/2024 23:10:00  2024/11/30 23:10:00  0.00   \n", "570236    2307536       0  30/11/2024 23:20:00  2024/11/30 23:20:00  0.00   \n", "570237    2307536       0  30/11/2024 23:30:00  2024/11/30 23:30:00  0.00   \n", "570238    2307536       0  30/11/2024 23:40:00  2024/11/30 23:40:00  0.00   \n", "570239    2307536       0  30/11/2024 23:50:00  2024/11/30 23:50:00  0.00   \n", "\n", "          GD    WD    OD  ER  SD  DD  ProductionG1  ProductionG2 ActPower  \\\n", "0       0.00  0.00  0.00   0   0   0     108482448             0    65.68   \n", "1       0.00  0.00  0.00   0   0   0     108482510             0    62.29   \n", "2       0.00  0.00  0.00   0   0   0     108482575             0    64.74   \n", "3       0.00  0.00  0.00   0   0   0     108482631             0    56.31   \n", "4       0.00  0.00  0.00   0   0   0     108482686             0    54.79   \n", "...      ...   ...   ...  ..  ..  ..           ...           ...      ...   \n", "570235  0.00  0.00  0.00   0   0   0      73799071             0    18.40   \n", "570236  0.00  0.00  0.00   0   0   0      73799093             0    22.77   \n", "570237  0.00  0.00  0.00   0   0   0      73799107             0    13.14   \n", "570238  0.00  0.00  0.00   0   0   0      73799118             0    11.81   \n", "570239  0.00  0.00  0.00   0   0   0      73799127             0     8.27   \n", "\n", "       ActPowerValue ProductionSource  ProductionSourceID  \\\n", "0              65.68      ActivePower                   3   \n", "1              62.29      ActivePower                   3   \n", "2              64.74      ActivePower                   3   \n", "3              56.31      ActivePower                   3   \n", "4              54.79      ActivePower                   3   \n", "...              ...              ...                 ...   \n", "570235         18.40      ActivePower                   3   \n", "570236         22.77      ActivePower                   3   \n", "570237         13.14      ActivePower                   3   \n", "570238         11.81      ActivePower                   3   \n", "570239          8.27      ActivePower                   3   \n", "\n", "        OrgProductionSourceID     AP  ChangePending ChangedBy  \\\n", "0                           3  65.68              0      None   \n", "1                           3  62.29              0      None   \n", "2                           3  64.74              0      None   \n", "3                           3  56.31              0      None   \n", "4                           3  54.79              0      None   \n", "...                       ...    ...            ...       ...   \n", "570235                      3  18.40              1      None   \n", "570236                      3  22.77              1      None   \n", "570237                      3  13.14              1      None   \n", "570238                      3  11.81              1      None   \n", "570239                      3   8.27              1      None   \n", "\n", "                ChangedDate       ChangedDateRaw  ProductionWarningID  \\\n", "0       2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "1       2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "2       2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "3       2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "4       2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "...                     ...                  ...                  ...   \n", "570235  2024/12/10 10:21:01  2024/12/10 10:21:01                    0   \n", "570236  2024/12/10 10:21:01  2024/12/10 10:21:01                    0   \n", "570237  2024/12/10 10:21:01  2024/12/10 10:21:01                    0   \n", "570238  2024/12/10 10:21:01  2024/12/10 10:21:01                    0   \n", "570239  2024/12/10 10:21:01  2024/12/10 10:21:01                    0   \n", "\n", "       ProductionWarning  \n", "0                   None  \n", "1                   None  \n", "2                   None  \n", "3                   None  \n", "4                   None  \n", "...                  ...  \n", "570235              None  \n", "570236              None  \n", "570237              None  \n", "570238              None  \n", "570239              None  \n", "\n", "[570240 rows x 51 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df_wd_data = pd.read_parquet(\"./WD_scrape_output/wd_data_20241101_to_20241201.parquet\")\n", "df_wd_data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ELR</th>\n", "      <th>StandStill</th>\n", "      <th>Grid</th>\n", "      <th>Wind</th>\n", "      <th>Other</th>\n", "      <th>Error</th>\n", "      <th>Service</th>\n", "      <th>PM</th>\n", "      <th>PEPVar</th>\n", "      <th>ELVar</th>\n", "      <th>PMVar</th>\n", "      <th>ActLimit</th>\n", "      <th>ELXC</th>\n", "      <th>PowerBoost</th>\n", "      <th>WAEP</th>\n", "      <th>ELGL</th>\n", "      <th>ELXGL</th>\n", "      <th>StationID</th>\n", "      <th>ParkID</th>\n", "      <th>ER</th>\n", "      <th>SD</th>\n", "      <th>DD</th>\n", "      <th>ProductionG1</th>\n", "      <th>ProductionG2</th>\n", "      <th>ProductionSourceID</th>\n", "      <th>OrgProductionSourceID</th>\n", "      <th>ChangePending</th>\n", "      <th>ProductionWarningID</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>570240.0</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.0</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.0</td>\n", "      <td>570240.0</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.0</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.0</td>\n", "      <td>570240.0</td>\n", "      <td>570240.0</td>\n", "      <td>5.702400e+05</td>\n", "      <td>570240.0</td>\n", "      <td>570240.0</td>\n", "      <td>570240.0</td>\n", "      <td>570240.0</td>\n", "      <td>5.702400e+05</td>\n", "      <td>570240.0</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.000000</td>\n", "      <td>570240.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.0</td>\n", "      <td>31.738345</td>\n", "      <td>4.200737</td>\n", "      <td>3.068829</td>\n", "      <td>4.347143</td>\n", "      <td>19.112491</td>\n", "      <td>0.0</td>\n", "      <td>0.935617</td>\n", "      <td>0.185904</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.003939</td>\n", "      <td>0.0</td>\n", "      <td>0.004673</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.307470e+06</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.520445e+07</td>\n", "      <td>0.0</td>\n", "      <td>3.010040</td>\n", "      <td>3.010040</td>\n", "      <td>0.357265</td>\n", "      <td>0.002778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.0</td>\n", "      <td>131.927256</td>\n", "      <td>49.317082</td>\n", "      <td>41.593276</td>\n", "      <td>46.274545</td>\n", "      <td>103.821953</td>\n", "      <td>0.0</td>\n", "      <td>0.245435</td>\n", "      <td>0.926454</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.062635</td>\n", "      <td>0.0</td>\n", "      <td>0.257684</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.810406e+01</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>8.271435e+06</td>\n", "      <td>0.0</td>\n", "      <td>0.105289</td>\n", "      <td>0.105289</td>\n", "      <td>0.479194</td>\n", "      <td>0.105373</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.307405e+06</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>-1.586680e+08</td>\n", "      <td>0.0</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.307438e+06</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.067425e+07</td>\n", "      <td>0.0</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.307470e+06</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.446825e+07</td>\n", "      <td>0.0</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.307503e+06</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.881280e+07</td>\n", "      <td>0.0</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>0.0</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>10.000000</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>20.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.307536e+06</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.102668e+08</td>\n", "      <td>0.0</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            ELR     StandStill           Grid           Wind          Other  \\\n", "count  570240.0  570240.000000  570240.000000  570240.000000  570240.000000   \n", "mean        0.0      31.738345       4.200737       3.068829       4.347143   \n", "std         0.0     131.927256      49.317082      41.593276      46.274545   \n", "min         0.0       0.000000       0.000000       0.000000       0.000000   \n", "25%         0.0       0.000000       0.000000       0.000000       0.000000   \n", "50%         0.0       0.000000       0.000000       0.000000       0.000000   \n", "75%         0.0       0.000000       0.000000       0.000000       0.000000   \n", "max         0.0     600.000000     600.000000     600.000000     600.000000   \n", "\n", "               Error   Service             PM         PEPVar     ELVar  \\\n", "count  570240.000000  570240.0  570240.000000  570240.000000  570240.0   \n", "mean       19.112491       0.0       0.935617       0.185904       1.0   \n", "std       103.821953       0.0       0.245435       0.926454       0.0   \n", "min         0.000000       0.0       0.000000       0.000000       1.0   \n", "25%         0.000000       0.0       1.000000       0.000000       1.0   \n", "50%         0.000000       0.0       1.000000       0.000000       1.0   \n", "75%         0.000000       0.0       1.000000       0.000000       1.0   \n", "max       600.000000       0.0       1.000000      10.000000       1.0   \n", "\n", "          PMVar       ActLimit      ELXC     PowerBoost      WAEP      ELGL  \\\n", "count  570240.0  570240.000000  570240.0  570240.000000  570240.0  570240.0   \n", "mean        1.0       0.003939       0.0       0.004673       0.0       0.0   \n", "std         0.0       0.062635       0.0       0.257684       0.0       0.0   \n", "min         1.0       0.000000       0.0       0.000000       0.0       0.0   \n", "25%         1.0       0.000000       0.0       0.000000       0.0       0.0   \n", "50%         1.0       0.000000       0.0       0.000000       0.0       0.0   \n", "75%         1.0       0.000000       0.0       0.000000       0.0       0.0   \n", "max         1.0       1.000000       0.0      20.000000       0.0       0.0   \n", "\n", "          ELXGL     StationID    ParkID        ER        SD        DD  \\\n", "count  570240.0  5.702400e+05  570240.0  570240.0  570240.0  570240.0   \n", "mean        0.0  2.307470e+06       0.0       0.0       0.0       0.0   \n", "std         0.0  3.810406e+01       0.0       0.0       0.0       0.0   \n", "min         0.0  2.307405e+06       0.0       0.0       0.0       0.0   \n", "25%         0.0  2.307438e+06       0.0       0.0       0.0       0.0   \n", "50%         0.0  2.307470e+06       0.0       0.0       0.0       0.0   \n", "75%         0.0  2.307503e+06       0.0       0.0       0.0       0.0   \n", "max         0.0  2.307536e+06       0.0       0.0       0.0       0.0   \n", "\n", "       ProductionG1  ProductionG2  ProductionSourceID  OrgProductionSourceID  \\\n", "count  5.702400e+05      570240.0       570240.000000          570240.000000   \n", "mean   9.520445e+07           0.0            3.010040               3.010040   \n", "std    8.271435e+06           0.0            0.105289               0.105289   \n", "min   -1.586680e+08           0.0            3.000000               3.000000   \n", "25%    9.067425e+07           0.0            3.000000               3.000000   \n", "50%    9.446825e+07           0.0            3.000000               3.000000   \n", "75%    9.881280e+07           0.0            3.000000               3.000000   \n", "max    1.102668e+08           0.0            6.000000               6.000000   \n", "\n", "       ChangePending  ProductionWarningID  \n", "count  570240.000000        570240.000000  \n", "mean        0.357265             0.002778  \n", "std         0.479194             0.105373  \n", "min         0.000000             0.000000  \n", "25%         0.000000             0.000000  \n", "50%         0.000000             0.000000  \n", "75%         1.000000             0.000000  \n", "max         1.000000             4.000000  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df_wd_data.describe()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No duplicate rows found\n"]}], "source": ["# Check for duplicates across key columns like StationID, TimestampStart and relevant metrics\n", "duplicate_mask = df_wd_data.duplicated(subset=['StationID', 'TimestampStart', 'PEP', 'ActPower'], keep=False)\n", "duplicates = df_wd_data[duplicate_mask].sort_values(['StationID', 'TimestampStart'])\n", "\n", "if len(duplicates) > 0:\n", "    print(f\"Found {len(duplicates)} duplicate rows\")\n", "    display(duplicates)\n", "else:\n", "    print(\"No duplicate rows found\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["StationID\n", "2307405    4320\n", "2307406    4320\n", "2307407    4320\n", "2307408    4320\n", "2307409    4320\n", "           ... \n", "2307532    4320\n", "2307533    4320\n", "2307534    4320\n", "2307535    4320\n", "2307536    4320\n", "Length: 132, dtype: int64"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df_wd_data.groupby('StationID').size().sort_values()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>YBA</th>\n", "      <th>EL</th>\n", "      <th>ELX</th>\n", "      <th>ELR</th>\n", "      <th>ELXR</th>\n", "      <th>PEP</th>\n", "      <th>OrgPEP</th>\n", "      <th>StandStill</th>\n", "      <th>Grid</th>\n", "      <th>Wind</th>\n", "      <th>Other</th>\n", "      <th>Error</th>\n", "      <th>Service</th>\n", "      <th>PM</th>\n", "      <th>PEPVar</th>\n", "      <th>ELVar</th>\n", "      <th>PMVar</th>\n", "      <th>ActLimit</th>\n", "      <th>Duration</th>\n", "      <th>PowerRed</th>\n", "      <th>VariantName</th>\n", "      <th>ELXC</th>\n", "      <th>PowerBoost</th>\n", "      <th>WAEP</th>\n", "      <th>ELGL</th>\n", "      <th>ELXGL</th>\n", "      <th>StationID</th>\n", "      <th>ParkID</th>\n", "      <th>TimestampStart</th>\n", "      <th>TimestampStartRaw</th>\n", "      <th>SS</th>\n", "      <th>GD</th>\n", "      <th>WD</th>\n", "      <th>OD</th>\n", "      <th>ER</th>\n", "      <th>SD</th>\n", "      <th>DD</th>\n", "      <th>ProductionG1</th>\n", "      <th>ProductionG2</th>\n", "      <th>ActPower</th>\n", "      <th>ActPowerValue</th>\n", "      <th>ProductionSource</th>\n", "      <th>ProductionSourceID</th>\n", "      <th>OrgProductionSourceID</th>\n", "      <th>AP</th>\n", "      <th>ChangePending</th>\n", "      <th>ChangedBy</th>\n", "      <th>ChangedDate</th>\n", "      <th>ChangedDateRaw</th>\n", "      <th>ProductionWarningID</th>\n", "      <th>ProductionWarning</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>63.50</td>\n", "      <td>63.50</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 02:30:00</td>\n", "      <td>2024/11/01 02:30:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108483295</td>\n", "      <td>0</td>\n", "      <td>63.50</td>\n", "      <td>63.50</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>63.50</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>60.00</td>\n", "      <td>60.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 02:20:00</td>\n", "      <td>2024/11/01 02:20:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108483232</td>\n", "      <td>0</td>\n", "      <td>60.00</td>\n", "      <td>60.00</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>60.00</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>57.22</td>\n", "      <td>57.22</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 02:10:00</td>\n", "      <td>2024/11/01 02:10:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108483172</td>\n", "      <td>0</td>\n", "      <td>57.22</td>\n", "      <td>57.22</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>57.22</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>60.09</td>\n", "      <td>60.09</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 02:00:00</td>\n", "      <td>2024/11/01 02:00:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108483114</td>\n", "      <td>0</td>\n", "      <td>60.09</td>\n", "      <td>60.09</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>60.09</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>53.89</td>\n", "      <td>53.89</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307405</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 01:50:00</td>\n", "      <td>2024/11/01 01:50:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>108483054</td>\n", "      <td>0</td>\n", "      <td>53.89</td>\n", "      <td>53.89</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>53.89</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>2025/01/16 23:24:08</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>565931</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 01:50:00</td>\n", "      <td>2024/11/01 01:50:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73296406</td>\n", "      <td>0</td>\n", "      <td>-1.51</td>\n", "      <td>-1.51</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>-1.51</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>565932</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 02:00:00</td>\n", "      <td>2024/11/01 02:00:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73296404</td>\n", "      <td>0</td>\n", "      <td>-1.55</td>\n", "      <td>-1.55</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>-1.55</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>565933</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 02:10:00</td>\n", "      <td>2024/11/01 02:10:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73296403</td>\n", "      <td>0</td>\n", "      <td>-1.53</td>\n", "      <td>-1.53</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>-1.53</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>565934</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 02:20:00</td>\n", "      <td>2024/11/01 02:20:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73296401</td>\n", "      <td>0</td>\n", "      <td>-1.57</td>\n", "      <td>-1.57</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>-1.57</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>565935</th>\n", "      <td>100.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>2300.00</td>\n", "      <td>PEP=EnergyProduced</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2307536</td>\n", "      <td>0</td>\n", "      <td>1/11/2024 02:30:00</td>\n", "      <td>2024/11/01 02:30:00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>73296400</td>\n", "      <td>0</td>\n", "      <td>-1.54</td>\n", "      <td>-1.54</td>\n", "      <td>ActivePower</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>-1.54</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>2024/11/12 00:33:58</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>570240 rows × 51 columns</p>\n", "</div>"], "text/plain": ["           YBA    EL   ELX  ELR  ELXR    PEP OrgPEP  StandStill  Grid  Wind  \\\n", "15      100.00  0.00  0.00    0  0.00  63.50  63.50           0     0     0   \n", "14      100.00  0.00  0.00    0  0.00  60.00  60.00           0     0     0   \n", "13      100.00  0.00  0.00    0  0.00  57.22  57.22           0     0     0   \n", "12      100.00  0.00  0.00    0  0.00  60.09  60.09           0     0     0   \n", "11      100.00  0.00  0.00    0  0.00  53.89  53.89           0     0     0   \n", "...        ...   ...   ...  ...   ...    ...    ...         ...   ...   ...   \n", "565931  100.00  0.00  0.00    0  0.00   0.00   0.00           0     0     0   \n", "565932  100.00  0.00  0.00    0  0.00   0.00   0.00           0     0     0   \n", "565933  100.00  0.00  0.00    0  0.00   0.00   0.00           0     0     0   \n", "565934  100.00  0.00  0.00    0  0.00   0.00   0.00           0     0     0   \n", "565935  100.00  0.00  0.00    0  0.00   0.00   0.00           0     0     0   \n", "\n", "        Other  Error  Service  PM  PEPVar  ELVar  PMVar  ActLimit Duration  \\\n", "15          0      0        0   1       0      1      1         0     0.00   \n", "14          0      0        0   1       0      1      1         0     0.00   \n", "13          0      0        0   1       0      1      1         0     0.00   \n", "12          0      0        0   1       0      1      1         0     0.00   \n", "11          0      0        0   1       0      1      1         0     0.00   \n", "...       ...    ...      ...  ..     ...    ...    ...       ...      ...   \n", "565931      0      0        0   1       0      1      1         0     0.00   \n", "565932      0      0        0   1       0      1      1         0     0.00   \n", "565933      0      0        0   1       0      1      1         0     0.00   \n", "565934      0      0        0   1       0      1      1         0     0.00   \n", "565935      0      0        0   1       0      1      1         0     0.00   \n", "\n", "       PowerRed         VariantName  ELXC  PowerBoost  WAEP  ELGL  ELXGL  \\\n", "15      2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "14      2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "13      2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "12      2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "11      2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "...         ...                 ...   ...         ...   ...   ...    ...   \n", "565931  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "565932  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "565933  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "565934  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "565935  2300.00  PEP=EnergyProduced     0           0     0     0      0   \n", "\n", "        StationID  ParkID      TimestampStart    TimestampStartRaw    SS  \\\n", "15        2307405       0  1/11/2024 02:30:00  2024/11/01 02:30:00  0.00   \n", "14        2307405       0  1/11/2024 02:20:00  2024/11/01 02:20:00  0.00   \n", "13        2307405       0  1/11/2024 02:10:00  2024/11/01 02:10:00  0.00   \n", "12        2307405       0  1/11/2024 02:00:00  2024/11/01 02:00:00  0.00   \n", "11        2307405       0  1/11/2024 01:50:00  2024/11/01 01:50:00  0.00   \n", "...           ...     ...                 ...                  ...   ...   \n", "565931    2307536       0  1/11/2024 01:50:00  2024/11/01 01:50:00  0.00   \n", "565932    2307536       0  1/11/2024 02:00:00  2024/11/01 02:00:00  0.00   \n", "565933    2307536       0  1/11/2024 02:10:00  2024/11/01 02:10:00  0.00   \n", "565934    2307536       0  1/11/2024 02:20:00  2024/11/01 02:20:00  0.00   \n", "565935    2307536       0  1/11/2024 02:30:00  2024/11/01 02:30:00  0.00   \n", "\n", "          GD    WD    OD  ER  SD  DD  ProductionG1  ProductionG2 ActPower  \\\n", "15      0.00  0.00  0.00   0   0   0     108483295             0    63.50   \n", "14      0.00  0.00  0.00   0   0   0     108483232             0    60.00   \n", "13      0.00  0.00  0.00   0   0   0     108483172             0    57.22   \n", "12      0.00  0.00  0.00   0   0   0     108483114             0    60.09   \n", "11      0.00  0.00  0.00   0   0   0     108483054             0    53.89   \n", "...      ...   ...   ...  ..  ..  ..           ...           ...      ...   \n", "565931  0.00  0.00  0.00   0   0   0      73296406             0    -1.51   \n", "565932  0.00  0.00  0.00   0   0   0      73296404             0    -1.55   \n", "565933  0.00  0.00  0.00   0   0   0      73296403             0    -1.53   \n", "565934  0.00  0.00  0.00   0   0   0      73296401             0    -1.57   \n", "565935  0.00  0.00  0.00   0   0   0      73296400             0    -1.54   \n", "\n", "       ActPowerValue ProductionSource  ProductionSourceID  \\\n", "15             63.50      ActivePower                   3   \n", "14             60.00      ActivePower                   3   \n", "13             57.22      ActivePower                   3   \n", "12             60.09      ActivePower                   3   \n", "11             53.89      ActivePower                   3   \n", "...              ...              ...                 ...   \n", "565931         -1.51      ActivePower                   3   \n", "565932         -1.55      ActivePower                   3   \n", "565933         -1.53      ActivePower                   3   \n", "565934         -1.57      ActivePower                   3   \n", "565935         -1.54      ActivePower                   3   \n", "\n", "        OrgProductionSourceID     AP  ChangePending ChangedBy  \\\n", "15                          3  63.50              0      None   \n", "14                          3  60.00              0      None   \n", "13                          3  57.22              0      None   \n", "12                          3  60.09              0      None   \n", "11                          3  53.89              0      None   \n", "...                       ...    ...            ...       ...   \n", "565931                      3  -1.51              0      None   \n", "565932                      3  -1.55              0      None   \n", "565933                      3  -1.53              0      None   \n", "565934                      3  -1.57              0      None   \n", "565935                      3  -1.54              0      None   \n", "\n", "                ChangedDate       ChangedDateRaw  ProductionWarningID  \\\n", "15      2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "14      2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "13      2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "12      2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "11      2025/01/16 23:24:08  2025/01/16 23:24:08                    0   \n", "...                     ...                  ...                  ...   \n", "565931  2024/11/12 00:33:58  2024/11/12 00:33:58                    0   \n", "565932  2024/11/12 00:33:58  2024/11/12 00:33:58                    0   \n", "565933  2024/11/12 00:33:58  2024/11/12 00:33:58                    0   \n", "565934  2024/11/12 00:33:58  2024/11/12 00:33:58                    0   \n", "565935  2024/11/12 00:33:58  2024/11/12 00:33:58                    0   \n", "\n", "       ProductionWarning  \n", "15                  None  \n", "14                  None  \n", "13                  None  \n", "12                  None  \n", "11                  None  \n", "...                  ...  \n", "565931              None  \n", "565932              None  \n", "565933              None  \n", "565934              None  \n", "565935              None  \n", "\n", "[570240 rows x 51 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df_wd_data.sort_values(\"StationID\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["132"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}